<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Http\Middleware\RedirectIncorrectLanguagePrefix;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

class TestRedirectMiddleware extends Command
{
    protected $signature = 'test:redirect-middleware';
    protected $description = 'Test the redirect middleware logic';

    public function handle()
    {
        $this->info('=== TESTING REDIRECT MIDDLEWARE ===');
        $this->newLine();

        $middleware = new RedirectIncorrectLanguagePrefix();

        // Test cases
        $testCases = [
            [
                'url' => '/ru/mietobjekte/france-paris',
                'description' => 'Russian URL with German prefix (should redirect)',
                'expected' => 'Redirect to /ru/arenda-nedvizimosti/france-paris'
            ],
            [
                'url' => '/ru/arenda-nedvizimosti/france-paris',
                'description' => 'Russian URL with correct prefix (should pass through)',
                'expected' => 'No redirect'
            ],
            [
                'url' => '/de/mietobjekte/france-paris',
                'description' => 'German URL with correct prefix (should pass through)',
                'expected' => 'No redirect'
            ],
            [
                'url' => '/de/arenda-nedvizimosti/france-paris',
                'description' => 'German URL with Russian prefix (should redirect)',
                'expected' => 'Redirect to /de/mietobjekte/france-paris'
            ],
            [
                'url' => '/en/rent-properties/france-paris',
                'description' => 'English URL with correct prefix (should pass through)',
                'expected' => 'No redirect'
            ],
            [
                'url' => '/',
                'description' => 'Homepage (should pass through)',
                'expected' => 'No redirect'
            ],
            [
                'url' => '/admin/dashboard',
                'description' => 'Admin URL (should pass through)',
                'expected' => 'No redirect'
            ]
        ];

        foreach ($testCases as $testCase) {
            $this->info("Testing: {$testCase['description']}");
            $this->line("  URL: {$testCase['url']}");
            $this->line("  Expected: {$testCase['expected']}");
            
            try {
                // Create a mock request
                $request = Request::create($testCase['url'], 'GET');
                
                // Test the middleware
                $response = $middleware->handle($request, function ($req) {
                    return response('Pass through');
                });
                
                if ($response->getStatusCode() === 301) {
                    $location = $response->headers->get('Location');
                    $this->line("  Result: ✅ REDIRECT to {$location}");
                } else {
                    $this->line("  Result: ✅ PASS THROUGH (no redirect)");
                }
                
            } catch (\Exception $e) {
                $this->line("  Result: ❌ ERROR - {$e->getMessage()}");
            }
            
            $this->newLine();
        }
        
        $this->info('=== TEST COMPLETE ===');
        $this->line('If the results match expectations, the middleware is working correctly.');
    }
}
