<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use <PERSON><PERSON>r\Slug\Models\Slug;
use Xmetr\Language\Facades\Language;
use Xmetr\Slug\Facades\SlugHelper;

class TestSlugResolution extends Command
{
    protected $signature = 'test:slug-resolution';
    protected $description = 'Test how slug resolution works for different language contexts';

    public function handle()
    {
        $this->info('=== TESTING SLUG RESOLUTION BY LANGUAGE ===');
        $this->newLine();

        // Test the specific case
        $key = 'france-paris';
        $prefixes = ['mietobjekte', 'arenda-nedvizimosti'];
        $languages = ['ru', 'de', 'en'];

        foreach ($languages as $lang) {
            $this->info("Testing with language context: {$lang}");
            
            // Set the current language context
            Language::setCurrentLocale($lang);
            
            foreach ($prefixes as $prefix) {
                $this->line("  Testing prefix '{$prefix}' with key '{$key}':");
                
                // Test direct slug lookup
                $slug = SlugHelper::getSlug($key, $prefix);
                
                if ($slug) {
                    $this->line("    FOUND: Slug ID {$slug->id}");
                    
                    // Check which translation matched
                    $translation = DB::table('slugs_translations')
                        ->where('slugs_id', $slug->id)
                        ->where('key', $key)
                        ->where('prefix', $prefix)
                        ->get();
                    
                    if ($translation->count() > 0) {
                        foreach ($translation as $trans) {
                            $this->line("      Via translation: Lang {$trans->lang_code}, Prefix '{$trans->prefix}'");
                        }
                    } else {
                        $this->line("      Via main slug table: Prefix '{$slug->prefix}'");
                    }
                } else {
                    $this->line("    NOT FOUND");
                }
            }
            
            $this->newLine();
        }

        // Reset language
        Language::setCurrentLocale('en');
        
        $this->info('=== ANALYSIS ===');
        $this->line('The issue is that the slug resolution is not language-aware.');
        $this->line('When visiting /ru/mietobjekte/france-paris, it finds the German translation');
        $this->line('even though we are in Russian language context.');
        $this->newLine();
        
        $this->info('=== RECOMMENDATION ===');
        $this->line('The system should only match slugs for the current language context.');
        $this->line('This requires modifying the slug resolution logic to be language-aware.');
    }
}
