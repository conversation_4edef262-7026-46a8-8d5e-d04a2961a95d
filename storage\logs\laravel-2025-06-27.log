[2025-06-27 14:55:41] local.ERROR: The "-e" option does not exist. {"exception":"[object] (Symfony\\Component\\Console\\Exception\\RuntimeException(code: 0): The \"-e\" option does not exist. at D:\\laragon\\www\\xmetr\\vendor\\symfony\\console\\Input\\ArgvInput.php:125)
[stacktrace]
#0 D:\\laragon\\www\\xmetr\\vendor\\symfony\\console\\Input\\ArgvInput.php(107): Symfony\\Component\\Console\\Input\\ArgvInput->parseShortOptionSet('encodedCommand')
#1 D:\\laragon\\www\\xmetr\\vendor\\symfony\\console\\Input\\ArgvInput.php(87): Symfony\\Component\\Console\\Input\\ArgvInput->parseShortOption('-encodedCommand')
#2 D:\\laragon\\www\\xmetr\\vendor\\symfony\\console\\Input\\ArgvInput.php(74): Symfony\\Component\\Console\\Input\\ArgvInput->parseToken('-encodedCommand', true)
#3 D:\\laragon\\www\\xmetr\\vendor\\symfony\\console\\Input\\Input.php(56): Symfony\\Component\\Console\\Input\\ArgvInput->parse()
#4 D:\\laragon\\www\\xmetr\\vendor\\symfony\\console\\Command\\Command.php(285): Symfony\\Component\\Console\\Input\\Input->bind(Object(Symfony\\Component\\Console\\Input\\InputDefinition))
#5 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#6 D:\\laragon\\www\\xmetr\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#7 D:\\laragon\\www\\xmetr\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Laravel\\Tinker\\Console\\TinkerCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#8 D:\\laragon\\www\\xmetr\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#9 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#10 D:\\laragon\\www\\xmetr\\artisan(15): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#11 {main}
"} 
[2025-06-27 14:57:11] local.WARNING: DB query exceeded 615.18 ms. SQL: select `key`, `value` from `settings` where `key` like ?  
[2025-06-27 15:02:59] local.WARNING: DB query exceeded 2427.1 ms. SQL: select * from `audit_histories` order by `created_at` desc limit 10 offset 0  
[2025-06-27 15:26:56] local.WARNING: DB query exceeded 844.55 ms. SQL: select * from `re_properties` where (`re_properties`.`moderation_status` = ? and `re_properties`.`status` != ?) and (`expire_date` >= ? or `never_expired` = ?) and `status` != ? order by `created_at` desc limit 20 offset 540  
