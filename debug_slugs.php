<?php

// Investigation script for multilingual URL routing issue
// Run with: php artisan tinker < debug_slugs.php

use Illuminate\Support\Facades\DB;
use Xmetr\Slug\Models\Slug;
use Xmetr\Language\Facades\Language;
use Xmetr\Slug\Facades\SlugHelper;

echo "=== MULTILINGUAL URL ROUTING INVESTIGATION ===\n\n";

echo "1. Checking slugs for 'france-paris' ===\n";
$slugs = Slug::where('key', 'france-paris')->get();
if ($slugs->count() > 0) {
    foreach ($slugs as $slug) {
        echo "   ID: {$slug->id}, Key: {$slug->key}, Prefix: '{$slug->prefix}', Reference: {$slug->reference_type}#{$slug->reference_id}\n";
    }
} else {
    echo "   No slugs found for 'france-paris'\n";
}

echo "\n2. Checking slug translations for 'france-paris' ===\n";
$translations = DB::table('slugs_translations')->where('key', 'france-paris')->get();
if ($translations->count() > 0) {
    foreach ($translations as $trans) {
        echo "   Slug ID: {$trans->slugs_id}, Lang: {$trans->lang_code}, Key: {$trans->key}, Prefix: '{$trans->prefix}'\n";
    }
} else {
    echo "   No slug translations found for 'france-paris'\n";
}

echo "\n3. Checking all unique prefixes in slugs table ===\n";
$prefixes = Slug::select('prefix')->distinct()->whereNotNull('prefix')->where('prefix', '!=', '')->orderBy('prefix')->get();
foreach ($prefixes as $prefix) {
    echo "   Main Prefix: '{$prefix->prefix}'\n";
}

echo "\n4. Checking all unique prefixes in slug translations ===\n";
$transPrefixes = DB::table('slugs_translations')->select('prefix')->distinct()->whereNotNull('prefix')->where('prefix', '!=', '')->orderBy('prefix')->get();
foreach ($transPrefixes as $prefix) {
    echo "   Translation Prefix: '{$prefix->prefix}'\n";
}

echo "\n5. Checking permalink settings ===\n";
$settings = DB::table('settings')->where('key', 'like', 'permalink%')->orderBy('key')->get(['key', 'value']);
if ($settings->count() > 0) {
    foreach ($settings as $setting) {
        echo "   Setting: {$setting->key} = '{$setting->value}'\n";
    }
} else {
    echo "   No permalink settings found\n";
}

echo "\n6. Checking active languages ===\n";
$languages = DB::table('languages')->orderBy('lang_order')->get(['lang_code', 'lang_name', 'lang_is_default', 'lang_locale']);
foreach ($languages as $lang) {
    $default = $lang->lang_is_default ? ' (DEFAULT)' : '';
    echo "   Language: {$lang->lang_code} - {$lang->lang_name} [{$lang->lang_locale}]{$default}\n";
}

echo "\n7. Testing slug resolution for both prefixes ===\n";
echo "   Testing 'mietobjekte' + 'france-paris':\n";
$slug1 = SlugHelper::getSlug('france-paris', 'mietobjekte');
if ($slug1) {
    echo "     FOUND: ID {$slug1->id}, Reference: {$slug1->reference_type}#{$slug1->reference_id}\n";
} else {
    echo "     NOT FOUND\n";
}

echo "   Testing 'arenda-nedvizimosti' + 'france-paris':\n";
$slug2 = SlugHelper::getSlug('france-paris', 'arenda-nedvizimosti');
if ($slug2) {
    echo "     FOUND: ID {$slug2->id}, Reference: {$slug2->reference_type}#{$slug2->reference_id}\n";
} else {
    echo "     NOT FOUND\n";
}

echo "\n8. Checking Property model prefix configuration ===\n";
$propertyClass = 'Xmetr\\RealEstate\\Models\\Property';
$defaultPrefix = SlugHelper::getPrefix($propertyClass, '', false);
echo "   Default Property prefix (no translation): '{$defaultPrefix}'\n";
$translatedPrefix = SlugHelper::getPrefix($propertyClass, '', true);
echo "   Translated Property prefix: '{$translatedPrefix}'\n";

echo "\n9. Checking current language settings ===\n";
$currentLocale = Language::getCurrentLocale();
$defaultLocale = Language::getDefaultLocale();
echo "   Current locale: {$currentLocale}\n";
echo "   Default locale: {$defaultLocale}\n";

echo "\n=== INVESTIGATION COMPLETE ===\n";
