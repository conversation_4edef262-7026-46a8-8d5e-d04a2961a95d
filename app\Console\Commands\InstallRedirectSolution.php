<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\File;

class InstallRedirectSolution extends Command
{
    protected $signature = 'install:redirect-solution {--dry-run : Show what would be changed without making changes}';
    protected $description = 'Install the redirect solution for multilingual URL routing';

    public function handle()
    {
        $isDryRun = $this->option('dry-run');
        
        if ($isDryRun) {
            $this->info('=== DRY RUN MODE - No changes will be made ===');
        } else {
            $this->info('=== INSTALLING REDIRECT SOLUTION ===');
        }
        
        $this->newLine();

        // Check if middleware file exists
        $middlewarePath = 'app/Http/Middleware/RedirectIncorrectLanguagePrefix.php';
        if (!File::exists($middlewarePath)) {
            $this->error("Middleware file not found: {$middlewarePath}");
            $this->line('Please ensure the middleware file is created first.');
            return 1;
        }

        // Check if Kernel.php exists
        $kernelPath = 'app/Http/Kernel.php';
        if (!File::exists($kernelPath)) {
            $this->error("Kernel file not found: {$kernelPath}");
            return 1;
        }

        $kernelContent = File::get($kernelPath);
        
        // Check if middleware is already registered
        if (strpos($kernelContent, 'RedirectIncorrectLanguagePrefix') !== false) {
            $this->info('✅ Middleware is already registered in Kernel.php');
        } else {
            $this->info('📝 Need to register middleware in Kernel.php');
            
            // Find the web middleware group
            $pattern = "/'web' => \[(.*?)\],/s";
            if (preg_match($pattern, $kernelContent, $matches)) {
                $webMiddleware = $matches[1];
                
                // Add our middleware to the end
                $newWebMiddleware = rtrim($webMiddleware) . "\n            \\App\\Http\\Middleware\\RedirectIncorrectLanguagePrefix::class,\n        ";
                $newKernelContent = str_replace($matches[0], "'web' => [\n" . $newWebMiddleware . "],", $kernelContent);
                
                if ($isDryRun) {
                    $this->line('WOULD ADD to web middleware group:');
                    $this->line('\\App\\Http\\Middleware\\RedirectIncorrectLanguagePrefix::class,');
                } else {
                    File::put($kernelPath, $newKernelContent);
                    $this->info('✅ Middleware registered in Kernel.php');
                }
            } else {
                $this->error('Could not find web middleware group in Kernel.php');
                return 1;
            }
        }

        $this->newLine();
        
        if ($isDryRun) {
            $this->info('DRY RUN COMPLETE');
            $this->line('Run without --dry-run to apply the changes');
        } else {
            $this->info('✅ REDIRECT SOLUTION INSTALLED SUCCESSFULLY!');
            $this->newLine();
            $this->info('WHAT THIS SOLUTION DOES:');
            $this->line('✅ Redirects /ru/mietobjekte/france-paris → /ru/arenda-nedvizimosti/france-paris');
            $this->line('✅ Redirects /de/arenda-nedvizimosti/france-paris → /de/mietobjekte/france-paris');
            $this->line('✅ Allows correct URLs to pass through normally');
            $this->line('✅ Ignores admin and API routes');
            $this->line('✅ Preserves query parameters and additional path segments');
            $this->newLine();
            $this->warn('⚠️  IMPORTANT: Clear cache after installation:');
            $this->line('php artisan cache:clear');
            $this->line('php artisan config:clear');
            $this->newLine();
            $this->info('🧪 TEST THE SOLUTION:');
            $this->line('php artisan test:redirect-middleware');
        }

        return 0;
    }
}
