<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Xmetr\Slug\Models\Slug;
use Xmetr\Language\Facades\Language;
use Xmetr\Slug\Facades\SlugHelper;
use Illuminate\Support\Facades\App;

class DebugLanguageAwareQuery extends Command
{
    protected $signature = 'debug:language-aware-query';
    protected $description = 'Debug the language-aware slug query';

    public function handle()
    {
        $this->info('=== DEBUGGING LANGUAGE-AWARE SLUG QUERY ===');
        $this->newLine();

        // Test different language contexts
        $testCases = [
            ['locale' => 'en', 'prefix' => 'rent-properties', 'key' => 'france-paris'],
            ['locale' => 'ru', 'prefix' => 'arenda-nedvizimosti', 'key' => 'france-paris'],
            ['locale' => 'ru', 'prefix' => 'mietobjekte', 'key' => 'france-paris'],
            ['locale' => 'de', 'prefix' => 'mietobjekte', 'key' => 'france-paris'],
        ];

        foreach ($testCases as $test) {
            $this->info("Testing: {$test['locale']} / {$test['prefix']} / {$test['key']}");
            
            // Set language context
            App::setLocale($test['locale']);
            Language::setCurrentLocale($test['locale']);
            
            $this->line("  Current App locale: " . App::getLocale());
            $this->line("  Current Language locale: " . Language::getCurrentLocale());
            $this->line("  Default locale: " . Language::getDefaultLocale());
            
            // Get supported locales
            $supportedLocales = Language::getSupportedLocales();
            $this->line("  Supported locales: " . implode(', ', array_keys($supportedLocales)));
            
            // Test locale mapping
            $fullLocale = null;
            foreach ($supportedLocales as $localeCode => $localeData) {
                if (substr($localeCode, 0, 2) === $test['locale']) {
                    $fullLocale = $localeCode;
                    break;
                }
            }
            $this->line("  Mapped full locale: " . ($fullLocale ?: 'NOT FOUND'));
            
            // Test slug resolution
            $slug = SlugHelper::getSlug($test['key'], $test['prefix']);
            $this->line("  Slug result: " . ($slug ? "FOUND (ID {$slug->id})" : "NOT FOUND"));
            
            // Test direct database query
            $directQuery = Slug::where('key', $test['key'])
                ->where('prefix', $test['prefix'])
                ->first();
            $this->line("  Direct main table query: " . ($directQuery ? "FOUND (ID {$directQuery->id})" : "NOT FOUND"));
            
            // Test translation query
            if ($fullLocale) {
                $translationQuery = DB::table('slugs_translations')
                    ->where('key', $test['key'])
                    ->where('prefix', $test['prefix'])
                    ->where('lang_code', $fullLocale)
                    ->first();
                $this->line("  Direct translation query: " . ($translationQuery ? "FOUND (Slug ID {$translationQuery->slugs_id})" : "NOT FOUND"));
            }
            
            $this->newLine();
        }
        
        // Reset to default
        App::setLocale('en');
        Language::setCurrentLocale('en');
        
        $this->info('=== ANALYSIS ===');
        $this->line('If all queries return NOT FOUND, the language-aware filter is too restrictive.');
        $this->line('We may need to adjust the logic or revert the change.');
    }
}
