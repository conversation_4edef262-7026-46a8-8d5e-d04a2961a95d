<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use <PERSON><PERSON>r\Slug\Models\Slug;
use Xmetr\Language\Facades\Language;
use Xmetr\Slug\Facades\SlugHelper;

class DebugSlugs extends Command
{
    protected $signature = 'debug:slugs';
    protected $description = 'Debug multilingual URL routing issue';

    public function handle()
    {
        $this->info('=== MULTILINGUAL URL ROUTING INVESTIGATION ===');
        $this->newLine();

        $this->info('1. Checking slugs for "france-paris"');
        $slugs = Slug::where('key', 'france-paris')->get();
        if ($slugs->count() > 0) {
            foreach ($slugs as $slug) {
                $this->line("   ID: {$slug->id}, Key: {$slug->key}, Prefix: '{$slug->prefix}', Reference: {$slug->reference_type}#{$slug->reference_id}");
            }
        } else {
            $this->line('   No slugs found for "france-paris"');
        }

        $this->newLine();
        $this->info('2. Checking slug translations for "france-paris"');
        $translations = DB::table('slugs_translations')->where('key', 'france-paris')->get();
        if ($translations->count() > 0) {
            foreach ($translations as $trans) {
                $this->line("   Slug ID: {$trans->slugs_id}, Lang: {$trans->lang_code}, Key: {$trans->key}, Prefix: '{$trans->prefix}'");
            }
        } else {
            $this->line('   No slug translations found for "france-paris"');
        }

        $this->newLine();
        $this->info('3. Checking all unique prefixes in slugs table');
        $prefixes = Slug::select('prefix')->distinct()->whereNotNull('prefix')->where('prefix', '!=', '')->orderBy('prefix')->get();
        foreach ($prefixes as $prefix) {
            $this->line("   Main Prefix: '{$prefix->prefix}'");
        }

        $this->newLine();
        $this->info('4. Checking all unique prefixes in slug translations');
        $transPrefixes = DB::table('slugs_translations')->select('prefix')->distinct()->whereNotNull('prefix')->where('prefix', '!=', '')->orderBy('prefix')->get();
        foreach ($transPrefixes as $prefix) {
            $this->line("   Translation Prefix: '{$prefix->prefix}'");
        }

        $this->newLine();
        $this->info('5. Checking permalink settings');
        $settings = DB::table('settings')->where('key', 'like', 'permalink%')->orderBy('key')->get(['key', 'value']);
        if ($settings->count() > 0) {
            foreach ($settings as $setting) {
                $this->line("   Setting: {$setting->key} = '{$setting->value}'");
            }
        } else {
            $this->line('   No permalink settings found');
        }

        $this->newLine();
        $this->info('6. Checking active languages');
        $languages = DB::table('languages')->orderBy('lang_order')->get(['lang_code', 'lang_name', 'lang_is_default', 'lang_locale']);
        foreach ($languages as $lang) {
            $default = $lang->lang_is_default ? ' (DEFAULT)' : '';
            $this->line("   Language: {$lang->lang_code} - {$lang->lang_name} [{$lang->lang_locale}]{$default}");
        }

        $this->newLine();
        $this->info('7. Testing slug resolution for both prefixes');
        $this->line('   Testing "mietobjekte" + "france-paris":');
        $slug1 = SlugHelper::getSlug('france-paris', 'mietobjekte');
        if ($slug1) {
            $this->line("     FOUND: ID {$slug1->id}, Reference: {$slug1->reference_type}#{$slug1->reference_id}");
        } else {
            $this->line('     NOT FOUND');
        }

        $this->line('   Testing "arenda-nedvizimosti" + "france-paris":');
        $slug2 = SlugHelper::getSlug('france-paris', 'arenda-nedvizimosti');
        if ($slug2) {
            $this->line("     FOUND: ID {$slug2->id}, Reference: {$slug2->reference_type}#{$slug2->reference_id}");
        } else {
            $this->line('     NOT FOUND');
        }

        $this->newLine();
        $this->info('8. Checking Property model prefix configuration');
        $propertyClass = 'Xmetr\\RealEstate\\Models\\Property';
        $defaultPrefix = SlugHelper::getPrefix($propertyClass, '', false);
        $this->line("   Default Property prefix (no translation): '{$defaultPrefix}'");
        $translatedPrefix = SlugHelper::getPrefix($propertyClass, '', true);
        $this->line("   Translated Property prefix: '{$translatedPrefix}'");

        $this->newLine();
        $this->info('9. Checking current language settings');
        $currentLocale = Language::getCurrentLocale();
        $defaultLocale = Language::getDefaultLocale();
        $this->line("   Current locale: {$currentLocale}");
        $this->line("   Default locale: {$defaultLocale}");

        $this->newLine();
        $this->info('=== INVESTIGATION COMPLETE ===');
    }
}
