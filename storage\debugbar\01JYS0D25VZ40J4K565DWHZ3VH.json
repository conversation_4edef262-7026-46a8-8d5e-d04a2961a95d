{"__meta": {"id": "01JYS0D25VZ40J4K565DWHZ3VH", "datetime": "2025-06-27 15:34:22", "utime": **********.140676, "method": "PUT", "uri": "/admin/plugins/status?name=gone-guard", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751038456.128614, "end": **********.140696, "duration": 6.012082099914551, "duration_str": "6.01s", "measures": [{"label": "Booting", "start": 1751038456.128614, "relative_start": 0, "end": **********.081609, "relative_end": **********.081609, "duration": 0.****************, "duration_str": "953ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.081634, "relative_start": 0.****************, "end": **********.140699, "relative_end": 2.86102294921875e-06, "duration": 5.***************, "duration_str": "5.06s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.104146, "relative_start": 0.***************, "end": **********.150323, "relative_end": **********.150323, "duration": 0.046176910400390625, "duration_str": "46.18ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.134342, "relative_start": 6.***************, "end": **********.13698, "relative_end": **********.13698, "duration": 0.002638101577758789, "duration_str": "2.64ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "10.x", "tooltip": {"Laravel Version": "10.48.29", "PHP Version": "8.3.17", "Environment": "local", "Debug Mode": "Enabled", "URL": "xmetr.gc", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 500, "nb_statements": 843, "nb_visible_statements": 500, "nb_excluded_statements": 343, "nb_failed_statements": 0, "accumulated_duration": 2.3961299999999985, "accumulated_duration_str": "2.4s", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "# Query soft and hard limit for Debugbar are reached. Only the first 100 queries show details. Queries after the first 500 are ignored. Limits can be raised in the config (debugbar.options.db.soft/hard_limit).", "type": "info"}, {"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 76}], "start": **********.162451, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 0, "width_percent": 0.023}, {"sql": "select `key` from `settings`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 45}, {"index": 15, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 372}, {"index": 17, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 335}, {"index": 18, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": **********.777938, "duration": 0.05078, "duration_str": "50.78ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:45", "source": {"index": 14, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 45}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php:45", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "45"}, "connection": "xmetr", "explain": null, "start_percent": 0.023, "width_percent": 2.119}, {"sql": "update `settings` set `value` = '6eadd395dd057399bda782a98b2db2e1', `settings`.`updated_at` = '2025-06-27 15:34:17' where `key` = 'media_random_hash'", "type": "query", "params": [], "bindings": ["6eadd395dd057399bda782a98b2db2e1", "2025-06-27 15:34:17", "media_random_hash"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 372}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 335}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": **********.834002, "duration": 0.00561, "duration_str": "5.61ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php:55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 2.142, "width_percent": 0.234}, {"sql": "update `settings` set `value` = '0', `settings`.`updated_at` = '2025-06-27 15:34:17' where `key` = 'api_enabled'", "type": "query", "params": [], "bindings": ["0", "2025-06-27 15:34:17", "api_enabled"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 372}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 335}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": **********.8424861, "duration": 0.0076500000000000005, "duration_str": "7.65ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php:55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 2.376, "width_percent": 0.319}, {"sql": "update `settings` set `value` = '[\\\"language\\\",\\\"language-advanced\\\",\\\"ads\\\",\\\"analytics\\\",\\\"audit-log\\\",\\\"backup\\\",\\\"captcha\\\",\\\"contact\\\",\\\"cookie-consent\\\",\\\"faq\\\",\\\"location\\\",\\\"newsletter\\\",\\\"payment\\\",\\\"paypal\\\",\\\"paystack\\\",\\\"razorpay\\\",\\\"real-estate\\\",\\\"social-login\\\",\\\"sslcommerz\\\",\\\"stripe\\\",\\\"testimonial\\\",\\\"translation\\\",\\\"magic\\\",\\\"notification-plus\\\"]', `settings`.`updated_at` = '2025-06-27 15:34:17' where `key` = 'activated_plugins'", "type": "query", "params": [], "bindings": ["[\"language\",\"language-advanced\",\"ads\",\"analytics\",\"audit-log\",\"backup\",\"captcha\",\"contact\",\"cookie-consent\",\"faq\",\"location\",\"newsletter\",\"payment\",\"paypal\",\"paystack\",\"razorpay\",\"real-estate\",\"social-login\",\"sslcommerz\",\"stripe\",\"testimonial\",\"translation\",\"magic\",\"notification-plus\"]", "2025-06-27 15:34:17", "activated_plugins"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 372}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 335}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": **********.852889, "duration": 0.01844, "duration_str": "18.44ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php:55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 2.695, "width_percent": 0.77}, {"sql": "update `settings` set `value` = '0', `settings`.`updated_at` = '2025-06-27 15:34:17' where `key` = 'analytics_dashboard_widgets'", "type": "query", "params": [], "bindings": ["0", "2025-06-27 15:34:17", "analytics_dashboard_widgets"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 372}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 335}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": **********.873142, "duration": 0.00369, "duration_str": "3.69ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php:55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 3.465, "width_percent": 0.154}, {"sql": "update `settings` set `value` = '1', `settings`.`updated_at` = '2025-06-27 15:34:17' where `key` = 'enable_recaptcha_xmetr_contact_forms_fronts_contact_form'", "type": "query", "params": [], "bindings": ["1", "2025-06-27 15:34:17", "enable_recaptcha_xmetr_contact_forms_fronts_contact_form"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 372}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 335}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": **********.879378, "duration": 0.0058, "duration_str": "5.8ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php:55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 3.619, "width_percent": 0.242}, {"sql": "update `settings` set `value` = '1', `settings`.`updated_at` = '2025-06-27 15:34:17' where `key` = 'enable_recaptcha_xmetr_newsletter_forms_fronts_newsletter_form'", "type": "query", "params": [], "bindings": ["1", "2025-06-27 15:34:17", "enable_recaptcha_xmetr_newsletter_forms_fronts_newsletter_form"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 372}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 335}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": **********.887707, "duration": 0.0040999999999999995, "duration_str": "4.1ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php:55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 3.861, "width_percent": 0.171}, {"sql": "update `settings` set `value` = '[\\\"email\\\"]', `settings`.`updated_at` = '2025-06-27 15:34:17' where `key` = 'real_estate_mandatory_fields_at_consult_form'", "type": "query", "params": [], "bindings": ["[\"email\"]", "2025-06-27 15:34:17", "real_estate_mandatory_fields_at_consult_form"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 372}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 335}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": **********.8941832, "duration": 0.00751, "duration_str": "7.51ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php:55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 4.032, "width_percent": 0.313}, {"sql": "update `settings` set `value` = 'xmetr', `settings`.`updated_at` = '2025-06-27 15:34:17' where `key` = 'theme'", "type": "query", "params": [], "bindings": ["xmetr", "2025-06-27 15:34:17", "theme"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 372}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 335}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": **********.904263, "duration": 0.004070000000000001, "duration_str": "4.07ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php:55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 4.345, "width_percent": 0.17}, {"sql": "update `settings` set `value` = '0', `settings`.`updated_at` = '2025-06-27 15:34:17' where `key` = 'show_admin_bar'", "type": "query", "params": [], "bindings": ["0", "2025-06-27 15:34:17", "show_admin_bar"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 372}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 335}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": **********.9105902, "duration": 0.07876000000000001, "duration_str": "78.76ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php:55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 4.515, "width_percent": 3.287}, {"sql": "update `settings` set `value` = '0', `settings`.`updated_at` = '2025-06-27 15:34:17' where `key` = 'language_hide_default'", "type": "query", "params": [], "bindings": ["0", "2025-06-27 15:34:17", "language_hide_default"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 372}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 335}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": **********.991638, "duration": 0.0068200000000000005, "duration_str": "6.82ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php:55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 7.802, "width_percent": 0.285}, {"sql": "update `settings` set `value` = 'dropdown', `settings`.`updated_at` = '2025-06-27 15:34:18' where `key` = 'language_switcher_display'", "type": "query", "params": [], "bindings": ["dropdown", "2025-06-27 15:34:18", "language_switcher_display"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 372}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 335}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": 1751038458.0013602, "duration": 0.0039900000000000005, "duration_str": "3.99ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php:55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 8.087, "width_percent": 0.167}, {"sql": "update `settings` set `value` = 'all', `settings`.`updated_at` = '2025-06-27 15:34:18' where `key` = 'language_display'", "type": "query", "params": [], "bindings": ["all", "2025-06-27 15:34:18", "language_display"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 372}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 335}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": 1751038458.0080001, "duration": 0.00414, "duration_str": "4.14ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php:55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 8.253, "width_percent": 0.173}, {"sql": "update `settings` set `value` = '[]', `settings`.`updated_at` = '2025-06-27 15:34:18' where `key` = 'language_hide_languages'", "type": "query", "params": [], "bindings": ["[]", "2025-06-27 15:34:18", "language_hide_languages"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 372}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 335}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": 1751038458.0145528, "duration": 0.0043, "duration_str": "4.3ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php:55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 8.426, "width_percent": 0.179}, {"sql": "update `settings` set `value` = 'news', `settings`.`updated_at` = '2025-06-27 15:34:18' where `key` = 'permalink-xmetr-blog-models-post'", "type": "query", "params": [], "bindings": ["news", "2025-06-27 15:34:18", "permalink-xmetr-blog-models-post"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 372}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 335}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": 1751038458.021103, "duration": 0.00404, "duration_str": "4.04ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php:55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 8.606, "width_percent": 0.169}, {"sql": "update `settings` set `value` = 'news', `settings`.`updated_at` = '2025-06-27 15:34:18' where `key` = 'permalink-xmetr-blog-models-category'", "type": "query", "params": [], "bindings": ["news", "2025-06-27 15:34:18", "permalink-xmetr-blog-models-category"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 372}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 335}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": 1751038458.027363, "duration": 0.00437, "duration_str": "4.37ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php:55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 8.774, "width_percent": 0.182}, {"sql": "update `settings` set `value` = '1', `settings`.`updated_at` = '2025-06-27 15:34:18' where `key` = 'payment_cod_status'", "type": "query", "params": [], "bindings": ["1", "2025-06-27 15:34:18", "payment_cod_status"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 372}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 335}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": 1751038458.035127, "duration": 0.1993, "duration_str": "199ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php:55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 8.957, "width_percent": 8.318}, {"sql": "update `settings` set `value` = 'Please pay money directly to the postman, if you choose cash on delivery method (COD).', `settings`.`updated_at` = '2025-06-27 15:34:18' where `key` = 'payment_cod_description'", "type": "query", "params": [], "bindings": ["Please pay money directly to the postman, if you choose cash on delivery method (COD).", "2025-06-27 15:34:18", "payment_cod_description"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 372}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 335}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": 1751038458.2359152, "duration": 0.01168, "duration_str": "11.68ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php:55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 17.274, "width_percent": 0.487}, {"sql": "update `settings` set `value` = '1', `settings`.`updated_at` = '2025-06-27 15:34:18' where `key` = 'payment_bank_transfer_status'", "type": "query", "params": [], "bindings": ["1", "2025-06-27 15:34:18", "payment_bank_transfer_status"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 372}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 335}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": 1751038458.249649, "duration": 0.0039, "duration_str": "3.9ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php:55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 17.762, "width_percent": 0.163}, {"sql": "update `settings` set `value` = 'Please send money to our bank account: ACB - 69270 213 19.', `settings`.`updated_at` = '2025-06-27 15:34:18' where `key` = 'payment_bank_transfer_description'", "type": "query", "params": [], "bindings": ["Please send money to our bank account: ACB - 69270 213 19.", "2025-06-27 15:34:18", "payment_bank_transfer_description"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 372}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 335}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": 1751038458.255106, "duration": 0.00368, "duration_str": "3.68ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php:55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 17.924, "width_percent": 0.154}, {"sql": "update `settings` set `value` = 'stripe_checkout', `settings`.`updated_at` = '2025-06-27 15:34:18' where `key` = 'payment_stripe_payment_type'", "type": "query", "params": [], "bindings": ["stripe_checkout", "2025-06-27 15:34:18", "payment_stripe_payment_type"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 372}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 335}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": 1751038458.2600641, "duration": 0.0036, "duration_str": "3.6ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php:55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 18.078, "width_percent": 0.15}, {"sql": "update `settings` set `value` = 'XMetr – долгосрочная аренда недвижимости в Аргентине', `settings`.`updated_at` = '2025-06-27 15:34:18' where `key` = 'theme-homzen-site_title'", "type": "query", "params": [], "bindings": ["XMetr – долгосрочная аренда недвижимости в Аргентине", "2025-06-27 15:34:18", "theme-homzen-site_title"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 372}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 335}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": 1751038458.2664561, "duration": 0.00418, "duration_str": "4.18ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php:55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 18.228, "width_percent": 0.174}, {"sql": "update `settings` set `value` = 'Долгосрочная аренда квартир в Аргентине и Буэнос-Айресе.', `settings`.`updated_at` = '2025-06-27 15:34:18' where `key` = 'theme-homzen-seo_description'", "type": "query", "params": [], "bindings": ["Долгосрочная аренда квартир в Аргентине и Буэнос-Айресе.", "2025-06-27 15:34:18", "theme-homzen-seo_description"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 372}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 335}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": 1751038458.271978, "duration": 0.00377, "duration_str": "3.77ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php:55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 18.403, "width_percent": 0.157}, {"sql": "update `settings` set `value` = '©XMetr - все права защищены.', `settings`.`updated_at` = '2025-06-27 15:34:18' where `key` = 'theme-homzen-copyright'", "type": "query", "params": [], "bindings": ["©XMetr - все права защищены.", "2025-06-27 15:34:18", "theme-homzen-copyright"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 372}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 335}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": 1751038458.2770379, "duration": 0.0035299999999999997, "duration_str": "3.53ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php:55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 18.56, "width_percent": 0.147}, {"sql": "update `settings` set `value` = 'general/favicon-1.png', `settings`.`updated_at` = '2025-06-27 15:34:18' where `key` = 'theme-homzen-favicon'", "type": "query", "params": [], "bindings": ["general/favicon-1.png", "2025-06-27 15:34:18", "theme-homzen-favicon"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 372}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 335}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": 1751038458.282142, "duration": 0.00394, "duration_str": "3.94ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php:55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 18.707, "width_percent": 0.164}, {"sql": "update `settings` set `value` = 'general/header-logo2.png', `settings`.`updated_at` = '2025-06-27 15:34:18' where `key` = 'theme-homzen-logo'", "type": "query", "params": [], "bindings": ["general/header-logo2.png", "2025-06-27 15:34:18", "theme-homzen-logo"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 372}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 335}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": 1751038458.287434, "duration": 0.00609, "duration_str": "6.09ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php:55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 18.872, "width_percent": 0.254}, {"sql": "update `settings` set `value` = 'general/logo-light.png', `settings`.`updated_at` = '2025-06-27 15:34:18' where `key` = 'theme-homzen-logo_light'", "type": "query", "params": [], "bindings": ["general/logo-light.png", "2025-06-27 15:34:18", "theme-homzen-logo_light"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 372}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 335}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": 1751038458.294803, "duration": 0.00412, "duration_str": "4.12ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php:55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 19.126, "width_percent": 0.172}, {"sql": "update `settings` set `value` = 'no', `settings`.`updated_at` = '2025-06-27 15:34:18' where `key` = 'theme-homzen-preloader_enabled'", "type": "query", "params": [], "bindings": ["no", "2025-06-27 15:34:18", "theme-homzen-preloader_enabled"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 372}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 335}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": 1751038458.3007371, "duration": 0.00415, "duration_str": "4.15ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php:55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 19.298, "width_percent": 0.173}, {"sql": "update `settings` set `value` = 'v2', `settings`.`updated_at` = '2025-06-27 15:34:18' where `key` = 'theme-homzen-preloader_version'", "type": "query", "params": [], "bindings": ["v2", "2025-06-27 15:34:18", "theme-homzen-preloader_version"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 372}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 335}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": 1751038458.306581, "duration": 0.00608, "duration_str": "6.08ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php:55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 19.471, "width_percent": 0.254}, {"sql": "update `settings` set `value` = '[[{\\\"key\\\":\\\"name\\\",\\\"value\\\":\\\"Telegram\\\"},{\\\"key\\\":\\\"icon\\\",\\\"value\\\":\\\"ti ti-brand-telegram\\\"},{\\\"key\\\":\\\"url\\\",\\\"value\\\":\\\"https:\\\\/\\\\/t.me\\\\/xmetrcom\\\"},{\\\"key\\\":\\\"image\\\",\\\"value\\\":null},{\\\"key\\\":\\\"color\\\",\\\"value\\\":\\\"transparent\\\"},{\\\"key\\\":\\\"background-color\\\",\\\"value\\\":\\\"transparent\\\"}]]', `settings`.`updated_at` = '2025-06-27 15:34:18' where `key` = 'theme-homzen-social_links'", "type": "query", "params": [], "bindings": ["[[{\"key\":\"name\",\"value\":\"Telegram\"},{\"key\":\"icon\",\"value\":\"ti ti-brand-telegram\"},{\"key\":\"url\",\"value\":\"https:\\/\\/t.me\\/xmetrcom\"},{\"key\":\"image\",\"value\":null},{\"key\":\"color\",\"value\":\"transparent\"},{\"key\":\"background-color\",\"value\":\"transparent\"}]]", "2025-06-27 15:34:18", "theme-homzen-social_links"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 372}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 335}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": 1751038458.315084, "duration": 0.00433, "duration_str": "4.33ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php:55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 19.725, "width_percent": 0.181}, {"sql": "update `settings` set `value` = '[[{\\\"key\\\":\\\"social\\\",\\\"value\\\":\\\"facebook\\\"},{\\\"key\\\":\\\"icon\\\",\\\"value\\\":\\\"ti ti-brand-facebook\\\"}],[{\\\"key\\\":\\\"social\\\",\\\"value\\\":\\\"x\\\"},{\\\"key\\\":\\\"icon\\\",\\\"value\\\":\\\"ti ti-brand-x\\\"}],[{\\\"key\\\":\\\"social\\\",\\\"value\\\":\\\"pinterest\\\"},{\\\"key\\\":\\\"icon\\\",\\\"value\\\":\\\"ti ti-brand-pinterest\\\"}],[{\\\"key\\\":\\\"social\\\",\\\"value\\\":\\\"linkedin\\\"},{\\\"key\\\":\\\"icon\\\",\\\"value\\\":\\\"ti ti-brand-linkedin\\\"}],[{\\\"key\\\":\\\"social\\\",\\\"value\\\":\\\"whatsapp\\\"},{\\\"key\\\":\\\"icon\\\",\\\"value\\\":\\\"ti ti-brand-whatsapp\\\"}],[{\\\"key\\\":\\\"social\\\",\\\"value\\\":\\\"email\\\"},{\\\"key\\\":\\\"icon\\\",\\\"value\\\":\\\"ti ti-mail\\\"}]]', `settings`.`updated_at` = '2025-06-27 15:34:18' where `key` = 'theme-homzen-social_sharing'", "type": "query", "params": [], "bindings": ["[[{\"key\":\"social\",\"value\":\"facebook\"},{\"key\":\"icon\",\"value\":\"ti ti-brand-facebook\"}],[{\"key\":\"social\",\"value\":\"x\"},{\"key\":\"icon\",\"value\":\"ti ti-brand-x\"}],[{\"key\":\"social\",\"value\":\"pinterest\"},{\"key\":\"icon\",\"value\":\"ti ti-brand-pinterest\"}],[{\"key\":\"social\",\"value\":\"linkedin\"},{\"key\":\"icon\",\"value\":\"ti ti-brand-linkedin\"}],[{\"key\":\"social\",\"value\":\"whatsapp\"},{\"key\":\"icon\",\"value\":\"ti ti-brand-whatsapp\"}],[{\"key\":\"social\",\"value\":\"email\"},{\"key\":\"icon\",\"value\":\"ti ti-mail\"}]]", "2025-06-27 15:34:18", "theme-homzen-social_sharing"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 372}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 335}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": 1751038458.320764, "duration": 0.004070000000000001, "duration_str": "4.07ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php:55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 19.905, "width_percent": 0.17}, {"sql": "update `settings` set `value` = 'rgb(103, 28, 201)', `settings`.`updated_at` = '2025-06-27 15:34:18' where `key` = 'theme-homzen-primary_color'", "type": "query", "params": [], "bindings": ["rgb(103, 28, 201)", "2025-06-27 15:34:18", "theme-homzen-primary_color"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 372}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 335}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": 1751038458.326176, "duration": 0.0035299999999999997, "duration_str": "3.53ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php:55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 20.075, "width_percent": 0.147}, {"sql": "update `settings` set `value` = 'rgb(101, 28, 197)', `settings`.`updated_at` = '2025-06-27 15:34:18' where `key` = 'theme-homzen-hover_color'", "type": "query", "params": [], "bindings": ["rgb(101, 28, 197)", "2025-06-27 15:34:18", "theme-homzen-hover_color"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 372}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 335}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": 1751038458.3312712, "duration": 0.00381, "duration_str": "3.81ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php:55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 20.223, "width_percent": 0.159}, {"sql": "update `settings` set `value` = '#161e2d', `settings`.`updated_at` = '2025-06-27 15:34:18' where `key` = 'theme-homzen-footer_background_color'", "type": "query", "params": [], "bindings": ["#161e2d", "2025-06-27 15:34:18", "theme-homzen-footer_background_color"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 372}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 335}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": 1751038458.336631, "duration": 0.00383, "duration_str": "3.83ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php:55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 20.382, "width_percent": 0.16}, {"sql": "update `settings` set `value` = '', `settings`.`updated_at` = '2025-06-27 15:34:18' where `key` = 'theme-homzen-footer_background_image'", "type": "query", "params": [], "bindings": ["", "2025-06-27 15:34:18", "theme-homzen-footer_background_image"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 372}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 335}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": 1751038458.341813, "duration": 0.0037099999999999998, "duration_str": "3.71ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php:55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 20.541, "width_percent": 0.155}, {"sql": "update `settings` set `value` = '1', `settings`.`updated_at` = '2025-06-27 15:34:18' where `key` = 'theme-homzen-use_modal_for_authentication'", "type": "query", "params": [], "bindings": ["1", "2025-06-27 15:34:18", "theme-homzen-use_modal_for_authentication"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 372}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 335}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": 1751038458.34808, "duration": 0.00394, "duration_str": "3.94ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php:55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 20.696, "width_percent": 0.164}, {"sql": "update `settings` set `value` = '1', `settings`.`updated_at` = '2025-06-27 15:34:18' where `key` = 'theme-homzen-homepage_id'", "type": "query", "params": [], "bindings": ["1", "2025-06-27 15:34:18", "theme-homzen-homepage_id"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 372}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 335}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": 1751038458.3536618, "duration": 0.0043, "duration_str": "4.3ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php:55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 20.861, "width_percent": 0.179}, {"sql": "update `settings` set `value` = '6', `settings`.`updated_at` = '2025-06-27 15:34:18' where `key` = 'theme-homzen-blog_page_id'", "type": "query", "params": [], "bindings": ["6", "2025-06-27 15:34:18", "theme-homzen-blog_page_id"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 372}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 335}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": 1751038458.3593721, "duration": 0.00364, "duration_str": "3.64ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php:55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 21.04, "width_percent": 0.152}, {"sql": "update `settings` set `value` = '', `settings`.`updated_at` = '2025-06-27 15:34:18' where `key` = 'theme-homzen-hotline'", "type": "query", "params": [], "bindings": ["", "2025-06-27 15:34:18", "theme-homzen-hotline"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 372}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 335}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": 1751038458.364525, "duration": 0.00379, "duration_str": "3.79ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php:55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 21.192, "width_percent": 0.158}, {"sql": "update `settings` set `value` = '<EMAIL>', `settings`.`updated_at` = '2025-06-27 15:34:18' where `key` = 'theme-homzen-email'", "type": "query", "params": [], "bindings": ["<EMAIL>", "2025-06-27 15:34:18", "theme-homzen-email"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 372}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 335}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": 1751038458.3699498, "duration": 0.00395, "duration_str": "3.95ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php:55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 21.35, "width_percent": 0.165}, {"sql": "update `settings` set `value` = '#f7f7f7', `settings`.`updated_at` = '2025-06-27 15:34:18' where `key` = 'theme-homzen-breadcrumb_background_color'", "type": "query", "params": [], "bindings": ["#f7f7f7", "2025-06-27 15:34:18", "theme-homzen-breadcrumb_background_color"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 372}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 335}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": 1751038458.375371, "duration": 0.0038900000000000002, "duration_str": "3.89ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php:55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 21.515, "width_percent": 0.162}, {"sql": "update `settings` set `value` = '#161e2d', `settings`.`updated_at` = '2025-06-27 15:34:18' where `key` = 'theme-homzen-breadcrumb_text_color'", "type": "query", "params": [], "bindings": ["#161e2d", "2025-06-27 15:34:18", "theme-homzen-breadcrumb_text_color"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 372}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 335}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": 1751038458.381121, "duration": 0.00394, "duration_str": "3.94ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php:55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 21.677, "width_percent": 0.164}, {"sql": "update `settings` set `value` = '0', `settings`.`updated_at` = '2025-06-27 15:34:18' where `key` = 'theme-homzen-lazy_load_images'", "type": "query", "params": [], "bindings": ["0", "2025-06-27 15:34:18", "theme-homzen-lazy_load_images"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 372}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 335}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": 1751038458.386413, "duration": 0.00363, "duration_str": "3.63ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php:55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 21.842, "width_percent": 0.151}, {"sql": "update `settings` set `value` = 'general/placeholder.png', `settings`.`updated_at` = '2025-06-27 15:34:18' where `key` = 'theme-homzen-lazy_load_placeholder_image'", "type": "query", "params": [], "bindings": ["general/placeholder.png", "2025-06-27 15:34:18", "theme-homzen-lazy_load_placeholder_image"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 372}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 335}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": 1751038458.391572, "duration": 0.00375, "duration_str": "3.75ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php:55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 21.993, "width_percent": 0.157}, {"sql": "update `settings` set `value` = '0', `settings`.`updated_at` = '2025-06-27 15:34:18' where `key` = 'theme-homzen-newsletter_popup_enable'", "type": "query", "params": [], "bindings": ["0", "2025-06-27 15:34:18", "theme-homzen-newsletter_popup_enable"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 372}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 335}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": 1751038458.397717, "duration": 0.00412, "duration_str": "4.12ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php:55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 22.15, "width_percent": 0.172}, {"sql": "update `settings` set `value` = 'general/newsletter-image.jpg', `settings`.`updated_at` = '2025-06-27 15:34:18' where `key` = 'theme-homzen-newsletter_popup_image'", "type": "query", "params": [], "bindings": ["general/newsletter-image.jpg", "2025-06-27 15:34:18", "theme-homzen-newsletter_popup_image"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 372}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 335}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": 1751038458.403218, "duration": 0.00379, "duration_str": "3.79ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php:55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 22.322, "width_percent": 0.158}, {"sql": "update `settings` set `value` = 'Let’s join our newsletter!', `settings`.`updated_at` = '2025-06-27 15:34:18' where `key` = 'theme-homzen-newsletter_popup_title'", "type": "query", "params": [], "bindings": ["Let’s join our newsletter!", "2025-06-27 15:34:18", "theme-homzen-newsletter_popup_title"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 372}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 335}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": 1751038458.408411, "duration": 0.0038399999999999997, "duration_str": "3.84ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php:55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 22.48, "width_percent": 0.16}, {"sql": "update `settings` set `value` = 'Weekly Updates', `settings`.`updated_at` = '2025-06-27 15:34:18' where `key` = 'theme-homzen-newsletter_popup_subtitle'", "type": "query", "params": [], "bindings": ["Weekly Updates", "2025-06-27 15:34:18", "theme-homzen-newsletter_popup_subtitle"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 372}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 335}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": 1751038458.414413, "duration": 0.00405, "duration_str": "4.05ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php:55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 22.64, "width_percent": 0.169}, {"sql": "update `settings` set `value` = 'Do not worry we don’t spam!', `settings`.`updated_at` = '2025-06-27 15:34:18' where `key` = 'theme-homzen-newsletter_popup_description'", "type": "query", "params": [], "bindings": ["Do not worry we don’t spam!", "2025-06-27 15:34:18", "theme-homzen-newsletter_popup_description"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 372}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 335}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": 1751038458.419895, "duration": 0.00367, "duration_str": "3.67ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php:55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 22.809, "width_percent": 0.153}, {"sql": "update `settings` set `value` = '14', `settings`.`updated_at` = '2025-06-27 15:34:18' where `key` = 'theme-homzen-properties_list_page_id'", "type": "query", "params": [], "bindings": ["14", "2025-06-27 15:34:18", "theme-homzen-properties_list_page_id"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 372}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 335}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": 1751038458.425248, "duration": 0.00387, "duration_str": "3.87ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php:55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 22.962, "width_percent": 0.162}, {"sql": "update `settings` set `value` = '15', `settings`.`updated_at` = '2025-06-27 15:34:18' where `key` = 'theme-homzen-projects_list_page_id'", "type": "query", "params": [], "bindings": ["15", "2025-06-27 15:34:18", "theme-homzen-projects_list_page_id"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 372}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 335}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": 1751038458.431294, "duration": 0.00418, "duration_str": "4.18ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php:55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 23.124, "width_percent": 0.174}, {"sql": "update `settings` set `value` = '2024-10-09 15:29:15', `settings`.`updated_at` = '2025-06-27 15:34:18' where `key` = 'membership_authorization_at'", "type": "query", "params": [], "bindings": ["2024-10-09 15:29:15", "2025-06-27 15:34:18", "membership_authorization_at"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 372}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 335}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": 1751038458.4367821, "duration": 0.01047, "duration_str": "10.47ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php:55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 23.298, "width_percent": 0.437}, {"sql": "update `settings` set `value` = '1', `settings`.`updated_at` = '2025-06-27 15:34:18' where `key` = 'is_completed_get_started'", "type": "query", "params": [], "bindings": ["1", "2025-06-27 15:34:18", "is_completed_get_started"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 372}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 335}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": 1751038458.448552, "duration": 0.0035800000000000003, "duration_str": "3.58ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php:55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 23.735, "width_percent": 0.149}, {"sql": "update `settings` set `value` = 'no', `settings`.`updated_at` = '2025-06-27 15:34:18' where `key` = 'theme-homzen-enabled_back_to_top'", "type": "query", "params": [], "bindings": ["no", "2025-06-27 15:34:18", "theme-homzen-enabled_back_to_top"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 372}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 335}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": 1751038458.453593, "duration": 0.0037400000000000003, "duration_str": "3.74ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php:55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 23.885, "width_percent": 0.156}, {"sql": "update `settings` set `value` = 'M d, Y', `settings`.`updated_at` = '2025-06-27 15:34:18' where `key` = 'theme-homzen-date_format'", "type": "query", "params": [], "bindings": ["M d, Y", "2025-06-27 15:34:18", "theme-homzen-date_format"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 372}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 335}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": 1751038458.458796, "duration": 0.00413, "duration_str": "4.13ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php:55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 24.041, "width_percent": 0.172}, {"sql": "update `settings` set `value` = '0', `settings`.`updated_at` = '2025-06-27 15:34:18' where `key` = 'theme-homzen-show_site_name'", "type": "query", "params": [], "bindings": ["0", "2025-06-27 15:34:18", "theme-homzen-show_site_name"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 372}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 335}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": 1751038458.464768, "duration": 0.00401, "duration_str": "4.01ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php:55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 24.213, "width_percent": 0.167}, {"sql": "update `settings` set `value` = '', `settings`.`updated_at` = '2025-06-27 15:34:18' where `key` = 'theme-homzen-seo_title'", "type": "query", "params": [], "bindings": ["", "2025-06-27 15:34:18", "theme-homzen-seo_title"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 372}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 335}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": 1751038458.470169, "duration": 0.00382, "duration_str": "3.82ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php:55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 24.381, "width_percent": 0.159}, {"sql": "update `settings` set `value` = '1', `settings`.`updated_at` = '2025-06-27 15:34:18' where `key` = 'theme-homzen-seo_index'", "type": "query", "params": [], "bindings": ["1", "2025-06-27 15:34:18", "theme-homzen-seo_index"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 372}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 335}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": 1751038458.47543, "duration": 0.00396, "duration_str": "3.96ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php:55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 24.54, "width_percent": 0.165}, {"sql": "update `settings` set `value` = 'general/header-logo2.png', `settings`.`updated_at` = '2025-06-27 15:34:18' where `key` = 'theme-homzen-seo_og_image'", "type": "query", "params": [], "bindings": ["general/header-logo2.png", "2025-06-27 15:34:18", "theme-homzen-seo_og_image"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 372}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 335}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": 1751038458.481929, "duration": 0.00329, "duration_str": "3.29ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php:55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 24.705, "width_percent": 0.137}, {"sql": "update `settings` set `value` = '', `settings`.`updated_at` = '2025-06-27 15:34:18' where `key` = 'theme-homzen-term_and_privacy_policy_url'", "type": "query", "params": [], "bindings": ["", "2025-06-27 15:34:18", "theme-homzen-term_and_privacy_policy_url"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 372}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 335}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": 1751038458.4865818, "duration": 0.00307, "duration_str": "3.07ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php:55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 24.843, "width_percent": 0.128}, {"sql": "update `settings` set `value` = 'Roboto', `settings`.`updated_at` = '2025-06-27 15:34:18' where `key` = 'theme-homzen-tp_primary_font'", "type": "query", "params": [], "bindings": ["Roboto", "2025-06-27 15:34:18", "theme-homzen-tp_primary_font"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 372}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 335}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": 1751038458.49106, "duration": 0.0037, "duration_str": "3.7ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php:55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 24.971, "width_percent": 0.154}, {"sql": "update `settings` set `value` = 'Roboto', `settings`.`updated_at` = '2025-06-27 15:34:18' where `key` = 'theme-homzen-tp_heading_font'", "type": "query", "params": [], "bindings": ["Roboto", "2025-06-27 15:34:18", "theme-homzen-tp_heading_font"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 372}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 335}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": 1751038458.496443, "duration": 0.00387, "duration_str": "3.87ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php:55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 25.125, "width_percent": 0.162}, {"sql": "update `settings` set `value` = '80', `settings`.`updated_at` = '2025-06-27 15:34:18' where `key` = 'theme-homzen-tp_h1_size'", "type": "query", "params": [], "bindings": ["80", "2025-06-27 15:34:18", "theme-homzen-tp_h1_size"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 372}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 335}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": 1751038458.501542, "duration": 0.0038, "duration_str": "3.8ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php:55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 25.287, "width_percent": 0.159}, {"sql": "update `settings` set `value` = '55', `settings`.`updated_at` = '2025-06-27 15:34:18' where `key` = 'theme-homzen-tp_h2_size'", "type": "query", "params": [], "bindings": ["55", "2025-06-27 15:34:18", "theme-homzen-tp_h2_size"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 372}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 335}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": 1751038458.506737, "duration": 0.00364, "duration_str": "3.64ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php:55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 25.445, "width_percent": 0.152}, {"sql": "update `settings` set `value` = '40', `settings`.`updated_at` = '2025-06-27 15:34:18' where `key` = 'theme-homzen-tp_h3_size'", "type": "query", "params": [], "bindings": ["40", "2025-06-27 15:34:18", "theme-homzen-tp_h3_size"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 372}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 335}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": 1751038458.5121489, "duration": 0.00393, "duration_str": "3.93ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php:55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 25.597, "width_percent": 0.164}, {"sql": "update `settings` set `value` = '35', `settings`.`updated_at` = '2025-06-27 15:34:18' where `key` = 'theme-homzen-tp_h4_size'", "type": "query", "params": [], "bindings": ["35", "2025-06-27 15:34:18", "theme-homzen-tp_h4_size"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 372}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 335}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": 1751038458.51803, "duration": 0.00411, "duration_str": "4.11ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php:55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 25.761, "width_percent": 0.172}, {"sql": "update `settings` set `value` = '30', `settings`.`updated_at` = '2025-06-27 15:34:18' where `key` = 'theme-homzen-tp_h5_size'", "type": "query", "params": [], "bindings": ["30", "2025-06-27 15:34:18", "theme-homzen-tp_h5_size"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 372}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 335}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": 1751038458.524797, "duration": 0.00337, "duration_str": "3.37ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php:55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 25.933, "width_percent": 0.141}, {"sql": "update `settings` set `value` = '20', `settings`.`updated_at` = '2025-06-27 15:34:18' where `key` = 'theme-homzen-tp_h6_size'", "type": "query", "params": [], "bindings": ["20", "2025-06-27 15:34:18", "theme-homzen-tp_h6_size"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 372}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 335}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": 1751038458.530403, "duration": 0.00337, "duration_str": "3.37ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php:55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 26.073, "width_percent": 0.141}, {"sql": "update `settings` set `value` = '15', `settings`.`updated_at` = '2025-06-27 15:34:18' where `key` = 'theme-homzen-tp_body_size'", "type": "query", "params": [], "bindings": ["15", "2025-06-27 15:34:18", "theme-homzen-tp_body_size"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 372}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 335}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": 1751038458.535902, "duration": 0.00405, "duration_str": "4.05ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php:55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 26.214, "width_percent": 0.169}, {"sql": "update `settings` set `value` = '20', `settings`.`updated_at` = '2025-06-27 15:34:18' where `key` = 'theme-homzen-number_of_projects_per_page'", "type": "query", "params": [], "bindings": ["20", "2025-06-27 15:34:18", "theme-homzen-number_of_projects_per_page"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 372}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 335}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": 1751038458.541877, "duration": 0.00379, "duration_str": "3.79ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php:55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 26.383, "width_percent": 0.158}, {"sql": "update `settings` set `value` = '30', `settings`.`updated_at` = '2025-06-27 15:34:18' where `key` = 'theme-homzen-number_of_properties_per_page'", "type": "query", "params": [], "bindings": ["30", "2025-06-27 15:34:18", "theme-homzen-number_of_properties_per_page"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 372}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 335}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": 1751038458.548058, "duration": 0.0035299999999999997, "duration_str": "3.53ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php:55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 26.541, "width_percent": 0.147}, {"sql": "update `settings` set `value` = '8', `settings`.`updated_at` = '2025-06-27 15:34:18' where `key` = 'theme-homzen-number_of_related_projects'", "type": "query", "params": [], "bindings": ["8", "2025-06-27 15:34:18", "theme-homzen-number_of_related_projects"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 372}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 335}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": 1751038458.553141, "duration": 0.00367, "duration_str": "3.67ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php:55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 26.688, "width_percent": 0.153}, {"sql": "update `settings` set `value` = '8', `settings`.`updated_at` = '2025-06-27 15:34:18' where `key` = 'theme-homzen-number_of_related_properties'", "type": "query", "params": [], "bindings": ["8", "2025-06-27 15:34:18", "theme-homzen-number_of_related_properties"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 372}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 335}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": 1751038458.558158, "duration": 0.0035600000000000002, "duration_str": "3.56ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php:55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 26.842, "width_percent": 0.149}, {"sql": "update `settings` set `value` = '43.615134, -76.393186', `settings`.`updated_at` = '2025-06-27 15:34:18' where `key` = 'theme-homzen-latitude_longitude_center_on_properties_page'", "type": "query", "params": [], "bindings": ["43.615134, -76.393186", "2025-06-27 15:34:18", "theme-homzen-latitude_longitude_center_on_properties_page"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 372}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 335}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": 1751038458.563207, "duration": 0.0038599999999999997, "duration_str": "3.86ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php:55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 26.99, "width_percent": 0.161}, {"sql": "update `settings` set `value` = 'top-map', `settings`.`updated_at` = '2025-06-27 15:34:18' where `key` = 'theme-homzen-real_estate_property_listing_layout'", "type": "query", "params": [], "bindings": ["top-map", "2025-06-27 15:34:18", "theme-homzen-real_estate_property_listing_layout"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 372}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 335}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": 1751038458.569002, "duration": 0.0032400000000000003, "duration_str": "3.24ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php:55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 27.151, "width_percent": 0.135}, {"sql": "update `settings` set `value` = '3', `settings`.`updated_at` = '2025-06-27 15:34:18' where `key` = 'theme-homzen-real_estate_property_detail_layout'", "type": "query", "params": [], "bindings": ["3", "2025-06-27 15:34:18", "theme-homzen-real_estate_property_detail_layout"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 372}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 335}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": 1751038458.574434, "duration": 0.0024100000000000002, "duration_str": "2.41ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php:55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 27.286, "width_percent": 0.101}, {"sql": "update `settings` set `value` = 'top-map', `settings`.`updated_at` = '2025-06-27 15:34:18' where `key` = 'theme-homzen-real_estate_project_listing_layout'", "type": "query", "params": [], "bindings": ["top-map", "2025-06-27 15:34:18", "theme-homzen-real_estate_project_listing_layout"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 372}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 335}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": 1751038458.578172, "duration": 0.0030099999999999997, "duration_str": "3.01ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php:55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 27.387, "width_percent": 0.126}, {"sql": "update `settings` set `value` = 'yes', `settings`.`updated_at` = '2025-06-27 15:34:18' where `key` = 'theme-homzen-real_estate_show_map_on_single_detail_page'", "type": "query", "params": [], "bindings": ["yes", "2025-06-27 15:34:18", "theme-homzen-real_estate_show_map_on_single_detail_page"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 372}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 335}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": 1751038458.582645, "duration": 0.00349, "duration_str": "3.49ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php:55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 27.513, "width_percent": 0.146}, {"sql": "update `settings` set `value` = 'loichenko', `settings`.`updated_at` = '2025-06-27 15:34:18' where `key` = 'licensed_to'", "type": "query", "params": [], "bindings": ["<PERSON><PERSON><PERSON>", "2025-06-27 15:34:18", "licensed_to"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 372}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 335}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": 1751038458.587675, "duration": 0.00313, "duration_str": "3.13ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php:55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 27.658, "width_percent": 0.131}, {"sql": "update `settings` set `value` = '[\\\"<EMAIL>\\\"]', `settings`.`updated_at` = '2025-06-27 15:34:18' where `key` = 'admin_email'", "type": "query", "params": [], "bindings": ["[\"<EMAIL>\"]", "2025-06-27 15:34:18", "admin_email"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 372}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 335}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": 1751038458.5922618, "duration": 0.00393, "duration_str": "3.93ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php:55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 27.789, "width_percent": 0.164}, {"sql": "update `settings` set `value` = 'UTC', `settings`.`updated_at` = '2025-06-27 15:34:18' where `key` = 'time_zone'", "type": "query", "params": [], "bindings": ["UTC", "2025-06-27 15:34:18", "time_zone"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 372}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 335}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": 1751038458.599486, "duration": 0.00453, "duration_str": "4.53ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php:55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 27.953, "width_percent": 0.189}, {"sql": "update `settings` set `value` = 'ltr', `settings`.`updated_at` = '2025-06-27 15:34:18' where `key` = 'locale_direction'", "type": "query", "params": [], "bindings": ["ltr", "2025-06-27 15:34:18", "locale_direction"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 372}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 335}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": 1751038458.606174, "duration": 0.00393, "duration_str": "3.93ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php:55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 28.142, "width_percent": 0.164}, {"sql": "update `settings` set `value` = '0', `settings`.`updated_at` = '2025-06-27 15:34:18' where `key` = 'enable_send_error_reporting_via_email'", "type": "query", "params": [], "bindings": ["0", "2025-06-27 15:34:18", "enable_send_error_reporting_via_email"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 372}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 335}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": 1751038458.611414, "duration": 0.00329, "duration_str": "3.29ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php:55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 28.306, "width_percent": 0.137}, {"sql": "update `settings` set `value` = '0', `settings`.`updated_at` = '2025-06-27 15:34:18' where `key` = 'redirect_404_to_homepage'", "type": "query", "params": [], "bindings": ["0", "2025-06-27 15:34:18", "redirect_404_to_homepage"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 372}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 335}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": 1751038458.616143, "duration": 0.00222, "duration_str": "2.22ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php:55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 28.443, "width_percent": 0.093}, {"sql": "update `settings` set `value` = '30', `settings`.`updated_at` = '2025-06-27 15:34:18' where `key` = 'audit_log_data_retention_period'", "type": "query", "params": [], "bindings": ["30", "2025-06-27 15:34:18", "audit_log_data_retention_period"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 372}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 335}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": 1751038458.6197028, "duration": 0.0028599999999999997, "duration_str": "2.86ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php:55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 28.536, "width_percent": 0.119}, {"sql": "update `settings` set `value` = 'ru', `settings`.`updated_at` = '2025-06-27 15:34:18' where `key` = 'locale'", "type": "query", "params": [], "bindings": ["ru", "2025-06-27 15:34:18", "locale"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 372}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 335}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": 1751038458.624048, "duration": 0.00311, "duration_str": "3.11ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php:55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 28.655, "width_percent": 0.13}, {"sql": "update `settings` set `value` = '1', `settings`.`updated_at` = '2025-06-27 15:34:18' where `key` = 'language_show_default_item_if_current_version_not_existed'", "type": "query", "params": [], "bindings": ["1", "2025-06-27 15:34:18", "language_show_default_item_if_current_version_not_existed"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 372}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 335}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": 1751038458.6286268, "duration": 0.00307, "duration_str": "3.07ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php:55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 28.785, "width_percent": 0.128}, {"sql": "update `settings` set `value` = '0', `settings`.`updated_at` = '2025-06-27 15:34:18' where `key` = 'language_auto_detect_user_language'", "type": "query", "params": [], "bindings": ["0", "2025-06-27 15:34:18", "language_auto_detect_user_language"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 372}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 335}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": 1751038458.63312, "duration": 0.0035499999999999998, "duration_str": "3.55ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php:55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 28.913, "width_percent": 0.148}, {"sql": "update `settings` set `value` = '#f7f7f7', `settings`.`updated_at` = '2025-06-27 15:34:18' where `key` = 'theme-homzen-top_header_background_color'", "type": "query", "params": [], "bindings": ["#f7f7f7", "2025-06-27 15:34:18", "theme-homzen-top_header_background_color"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 372}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 335}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": 1751038458.637987, "duration": 0.00372, "duration_str": "3.72ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php:55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 29.061, "width_percent": 0.155}, {"sql": "update `settings` set `value` = '#161e2d', `settings`.`updated_at` = '2025-06-27 15:34:18' where `key` = 'theme-homzen-top_header_text_color'", "type": "query", "params": [], "bindings": ["#161e2d", "2025-06-27 15:34:18", "theme-homzen-top_header_text_color"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 372}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 335}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": 1751038458.643769, "duration": 0.00304, "duration_str": "3.04ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php:55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 29.217, "width_percent": 0.127}, {"sql": "update `settings` set `value` = '#ffffff', `settings`.`updated_at` = '2025-06-27 15:34:18' where `key` = 'theme-homzen-main_header_background_color'", "type": "query", "params": [], "bindings": ["#ffffff", "2025-06-27 15:34:18", "theme-homzen-main_header_background_color"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 372}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 335}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": 1751038458.648989, "duration": 0.0023599999999999997, "duration_str": "2.36ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php:55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 29.344, "width_percent": 0.098}, {"sql": "update `settings` set `value` = '#161e2d', `settings`.`updated_at` = '2025-06-27 15:34:18' where `key` = 'theme-homzen-main_header_text_color'", "type": "query", "params": [], "bindings": ["#161e2d", "2025-06-27 15:34:18", "theme-homzen-main_header_text_color"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 372}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 335}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": 1751038458.6533031, "duration": 0.0016899999999999999, "duration_str": "1.69ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php:55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 29.442, "width_percent": 0.071}, {"sql": "update `settings` set `value` = '#e4e4e4', `settings`.`updated_at` = '2025-06-27 15:34:18' where `key` = 'theme-homzen-main_header_border_color'", "type": "query", "params": [], "bindings": ["#e4e4e4", "2025-06-27 15:34:18", "theme-homzen-main_header_border_color"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 372}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 335}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": 1751038458.656472, "duration": 0.00296, "duration_str": "2.96ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php:55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 29.513, "width_percent": 0.124}, {"sql": "update `settings` set `value` = 'yes', `settings`.`updated_at` = '2025-06-27 15:34:18' where `key` = 'theme-homzen-cookie_consent_enable'", "type": "query", "params": [], "bindings": ["yes", "2025-06-27 15:34:18", "theme-homzen-cookie_consent_enable"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 372}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 335}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": 1751038458.66092, "duration": 0.027100000000000003, "duration_str": "27.1ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php:55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 29.636, "width_percent": 1.131}, {"sql": "update `settings` set `value` = 'minimal', `settings`.`updated_at` = '2025-06-27 15:34:18' where `key` = 'theme-homzen-cookie_consent_style'", "type": "query", "params": [], "bindings": ["minimal", "2025-06-27 15:34:18", "theme-homzen-cookie_consent_style"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 372}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 335}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": 1751038458.6895552, "duration": 0.0031, "duration_str": "3.1ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php:55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 30.767, "width_percent": 0.129}, {"sql": "update `settings` set `value` = '? Мы используем cookies, что бы сделать сайт лучше.', `settings`.`updated_at` = '2025-06-27 15:34:18' where `key` = 'theme-homzen-cookie_consent_message'", "type": "query", "params": [], "bindings": ["? Мы используем cookies, что бы сделать сайт лучше.", "2025-06-27 15:34:18", "theme-homzen-cookie_consent_message"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 372}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 335}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": 1751038458.694089, "duration": 0.00279, "duration_str": "2.79ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php:55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 30.896, "width_percent": 0.116}, {"sql": "update `settings` set `value` = 'Ок', `settings`.`updated_at` = '2025-06-27 15:34:18' where `key` = 'theme-homzen-cookie_consent_button_text'", "type": "query", "params": [], "bindings": ["Ок", "2025-06-27 15:34:18", "theme-homzen-cookie_consent_button_text"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 372}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 335}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": 1751038458.699086, "duration": 0.0033599999999999997, "duration_str": "3.36ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php:55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 31.013, "width_percent": 0.14}, {"sql": "update `settings` set `value` = '', `settings`.`updated_at` = '2025-06-27 15:34:18' where `key` = 'theme-homzen-cookie_consent_learn_more_url'", "type": "query", "params": [], "bindings": ["", "2025-06-27 15:34:18", "theme-homzen-cookie_consent_learn_more_url"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 372}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 335}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": 1751038458.703867, "duration": 0.00318, "duration_str": "3.18ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php:55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 31.153, "width_percent": 0.133}, {"sql": "update `settings` set `value` = '', `settings`.`updated_at` = '2025-06-27 15:34:18' where `key` = 'theme-homzen-cookie_consent_learn_more_text'", "type": "query", "params": [], "bindings": ["", "2025-06-27 15:34:18", "theme-homzen-cookie_consent_learn_more_text"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 372}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 335}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": 1751038458.708424, "duration": 0.00176, "duration_str": "1.76ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php:55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 31.286, "width_percent": 0.073}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038458.711482, "duration": 0.00152, "duration_str": "1.52ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 31.359, "width_percent": 0.063}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038458.7135441, "duration": 0.00309, "duration_str": "3.09ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 31.423, "width_percent": 0.129}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038458.717079, "duration": 0.003, "duration_str": "3ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 31.552, "width_percent": 0.125}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038458.720457, "duration": 0.00159, "duration_str": "1.59ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 31.677, "width_percent": 0.066}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038458.722437, "duration": 0.00289, "duration_str": "2.89ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 31.743, "width_percent": 0.121}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038458.72575, "duration": 0.00314, "duration_str": "3.14ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 31.864, "width_percent": 0.131}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038458.72942, "duration": 0.00299, "duration_str": "2.99ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 31.995, "width_percent": 0.125}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038458.733031, "duration": 0.00226, "duration_str": "2.26ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 32.12, "width_percent": 0.094}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038458.735911, "duration": 0.00184, "duration_str": "1.84ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 32.214, "width_percent": 0.077}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038458.738155, "duration": 0.00182, "duration_str": "1.82ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 32.291, "width_percent": 0.076}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038458.7403681, "duration": 0.00155, "duration_str": "1.55ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 32.367, "width_percent": 0.065}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038458.74229, "duration": 0.00279, "duration_str": "2.79ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 32.431, "width_percent": 0.116}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038458.745467, "duration": 0.00304, "duration_str": "3.04ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 32.548, "width_percent": 0.127}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038458.748998, "duration": 0.0033799999999999998, "duration_str": "3.38ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 32.675, "width_percent": 0.141}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038458.7527502, "duration": 0.00151, "duration_str": "1.51ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 32.816, "width_percent": 0.063}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038458.7546282, "duration": 0.00188, "duration_str": "1.88ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 32.879, "width_percent": 0.078}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038458.75687, "duration": 0.00152, "duration_str": "1.52ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 32.957, "width_percent": 0.063}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038458.758722, "duration": 0.0018700000000000001, "duration_str": "1.87ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 33.021, "width_percent": 0.078}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038458.760918, "duration": 0.00145, "duration_str": "1.45ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 33.099, "width_percent": 0.061}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038458.762679, "duration": 0.00157, "duration_str": "1.57ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 33.159, "width_percent": 0.066}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038458.764979, "duration": 0.0034100000000000003, "duration_str": "3.41ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 33.225, "width_percent": 0.142}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038458.769102, "duration": 0.0018700000000000001, "duration_str": "1.87ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 33.367, "width_percent": 0.078}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038458.7713642, "duration": 0.00185, "duration_str": "1.85ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 33.445, "width_percent": 0.077}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038458.773576, "duration": 0.00171, "duration_str": "1.71ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 33.522, "width_percent": 0.071}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038458.775654, "duration": 0.0017, "duration_str": "1.7ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 33.594, "width_percent": 0.071}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038458.777681, "duration": 0.00136, "duration_str": "1.36ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 33.665, "width_percent": 0.057}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038458.7794502, "duration": 0.0016799999999999999, "duration_str": "1.68ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 33.721, "width_percent": 0.07}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038458.781598, "duration": 0.036359999999999996, "duration_str": "36.36ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 33.792, "width_percent": 1.517}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038458.8183591, "duration": 0.00185, "duration_str": "1.85ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 35.309, "width_percent": 0.077}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038458.820591, "duration": 0.00191, "duration_str": "1.91ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 35.386, "width_percent": 0.08}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038458.822875, "duration": 0.00171, "duration_str": "1.71ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 35.466, "width_percent": 0.071}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038458.824984, "duration": 0.00171, "duration_str": "1.71ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 35.537, "width_percent": 0.071}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038458.827096, "duration": 0.00149, "duration_str": "1.49ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 35.609, "width_percent": 0.062}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038458.829022, "duration": 0.00166, "duration_str": "1.66ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 35.671, "width_percent": 0.069}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038458.8313, "duration": 0.0022, "duration_str": "2.2ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 35.74, "width_percent": 0.092}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038458.8339, "duration": 0.0016899999999999999, "duration_str": "1.69ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 35.832, "width_percent": 0.071}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038458.835967, "duration": 0.02614, "duration_str": "26.14ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 35.902, "width_percent": 1.091}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038458.8626988, "duration": 0.00182, "duration_str": "1.82ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 36.993, "width_percent": 0.076}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038458.864998, "duration": 0.0020800000000000003, "duration_str": "2.08ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 37.069, "width_percent": 0.087}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038458.867567, "duration": 0.0017, "duration_str": "1.7ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 37.156, "width_percent": 0.071}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038458.869616, "duration": 0.00161, "duration_str": "1.61ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 37.227, "width_percent": 0.067}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038458.871599, "duration": 0.00173, "duration_str": "1.73ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 37.294, "width_percent": 0.072}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038458.873718, "duration": 0.00165, "duration_str": "1.65ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 37.367, "width_percent": 0.069}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038458.8757899, "duration": 0.00164, "duration_str": "1.64ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 37.435, "width_percent": 0.068}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038458.877666, "duration": 0.0013700000000000001, "duration_str": "1.37ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 37.504, "width_percent": 0.057}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038458.879504, "duration": 0.00175, "duration_str": "1.75ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 37.561, "width_percent": 0.073}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038458.881886, "duration": 0.00213, "duration_str": "2.13ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 37.634, "width_percent": 0.089}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038458.8844528, "duration": 0.00165, "duration_str": "1.65ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 37.723, "width_percent": 0.069}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038458.886462, "duration": 0.00173, "duration_str": "1.73ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 37.792, "width_percent": 0.072}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038458.8885791, "duration": 0.00172, "duration_str": "1.72ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 37.864, "width_percent": 0.072}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038458.890692, "duration": 0.00163, "duration_str": "1.63ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 37.936, "width_percent": 0.068}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038458.892629, "duration": 0.0014199999999999998, "duration_str": "1.42ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 38.004, "width_percent": 0.059}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038458.894285, "duration": 0.0014299999999999998, "duration_str": "1.43ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 38.063, "width_percent": 0.06}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038458.896172, "duration": 0.0016699999999999998, "duration_str": "1.67ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 38.123, "width_percent": 0.07}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038458.898453, "duration": 0.00195, "duration_str": "1.95ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 38.192, "width_percent": 0.081}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038458.900795, "duration": 0.0020099999999999996, "duration_str": "2.01ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 38.274, "width_percent": 0.084}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038458.9031641, "duration": 0.0016699999999999998, "duration_str": "1.67ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 38.358, "width_percent": 0.07}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038458.905213, "duration": 0.00172, "duration_str": "1.72ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 38.427, "width_percent": 0.072}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038458.907323, "duration": 0.00159, "duration_str": "1.59ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 38.499, "width_percent": 0.066}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038458.909272, "duration": 0.00149, "duration_str": "1.49ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 38.566, "width_percent": 0.062}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038458.910999, "duration": 0.00138, "duration_str": "1.38ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 38.628, "width_percent": 0.058}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038458.9128401, "duration": 0.0017, "duration_str": "1.7ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 38.685, "width_percent": 0.071}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038458.915107, "duration": 0.0019299999999999999, "duration_str": "1.93ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 38.756, "width_percent": 0.081}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038458.917711, "duration": 0.00195, "duration_str": "1.95ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 38.837, "width_percent": 0.081}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038458.9200232, "duration": 0.0016799999999999999, "duration_str": "1.68ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 38.918, "width_percent": 0.07}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038458.922113, "duration": 0.00757, "duration_str": "7.57ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 38.988, "width_percent": 0.316}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038458.930363, "duration": 0.00705, "duration_str": "7.05ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 39.304, "width_percent": 0.294}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038458.937778, "duration": 0.00718, "duration_str": "7.18ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 39.598, "width_percent": 0.3}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038458.945311, "duration": 0.00731, "duration_str": "7.31ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 39.898, "width_percent": 0.305}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038458.952991, "duration": 0.0069900000000000006, "duration_str": "6.99ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 40.203, "width_percent": 0.292}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038458.9604092, "duration": 0.0071200000000000005, "duration_str": "7.12ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 40.495, "width_percent": 0.297}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038458.96802, "duration": 0.0072, "duration_str": "7.2ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 40.792, "width_percent": 0.3}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038458.975621, "duration": 0.00735, "duration_str": "7.35ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 41.093, "width_percent": 0.307}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038458.9834042, "duration": 0.00346, "duration_str": "3.46ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 41.399, "width_percent": 0.144}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038458.9872308, "duration": 0.00307, "duration_str": "3.07ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 41.544, "width_percent": 0.128}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038458.9906821, "duration": 0.0029500000000000004, "duration_str": "2.95ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 41.672, "width_percent": 0.123}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038458.994033, "duration": 0.00282, "duration_str": "2.82ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 41.795, "width_percent": 0.118}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038458.9973922, "duration": 0.00309, "duration_str": "3.09ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 41.913, "width_percent": 0.129}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.000886, "duration": 0.003, "duration_str": "3ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 42.042, "width_percent": 0.125}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.004277, "duration": 0.003, "duration_str": "3ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 42.167, "width_percent": 0.125}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.007647, "duration": 0.0029300000000000003, "duration_str": "2.93ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 42.292, "width_percent": 0.122}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.010921, "duration": 0.00285, "duration_str": "2.85ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 42.414, "width_percent": 0.119}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.014238, "duration": 0.00331, "duration_str": "3.31ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 42.533, "width_percent": 0.138}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.017975, "duration": 0.00309, "duration_str": "3.09ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 42.671, "width_percent": 0.129}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.021441, "duration": 0.0030299999999999997, "duration_str": "3.03ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 42.8, "width_percent": 0.126}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.024805, "duration": 0.0029300000000000003, "duration_str": "2.93ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 42.927, "width_percent": 0.122}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.028097, "duration": 0.0028399999999999996, "duration_str": "2.84ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 43.049, "width_percent": 0.119}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.031359, "duration": 0.00335, "duration_str": "3.35ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 43.168, "width_percent": 0.14}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.0351572, "duration": 0.00309, "duration_str": "3.09ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 43.307, "width_percent": 0.129}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.038635, "duration": 0.0031, "duration_str": "3.1ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 43.436, "width_percent": 0.129}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.0421522, "duration": 0.0029300000000000003, "duration_str": "2.93ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 43.566, "width_percent": 0.122}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.045401, "duration": 0.0029, "duration_str": "2.9ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 43.688, "width_percent": 0.121}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.0487, "duration": 0.00325, "duration_str": "3.25ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 43.809, "width_percent": 0.136}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.052355, "duration": 0.0029500000000000004, "duration_str": "2.95ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 43.945, "width_percent": 0.123}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.055746, "duration": 0.00317, "duration_str": "3.17ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 44.068, "width_percent": 0.132}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.059268, "duration": 0.00287, "duration_str": "2.87ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 44.2, "width_percent": 0.12}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.062419, "duration": 0.00281, "duration_str": "2.81ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 44.32, "width_percent": 0.117}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.0655808, "duration": 0.00373, "duration_str": "3.73ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 44.437, "width_percent": 0.156}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.06968, "duration": 0.0038599999999999997, "duration_str": "3.86ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 44.593, "width_percent": 0.161}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.073915, "duration": 0.00368, "duration_str": "3.68ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 44.754, "width_percent": 0.154}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.0779948, "duration": 0.0035800000000000003, "duration_str": "3.58ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 44.907, "width_percent": 0.149}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.0821018, "duration": 0.0037400000000000003, "duration_str": "3.74ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 45.057, "width_percent": 0.156}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.086425, "duration": 0.0038799999999999998, "duration_str": "3.88ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 45.213, "width_percent": 0.162}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.090771, "duration": 0.0038599999999999997, "duration_str": "3.86ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 45.375, "width_percent": 0.161}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.095031, "duration": 0.0037099999999999998, "duration_str": "3.71ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 45.536, "width_percent": 0.155}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.099354, "duration": 0.00431, "duration_str": "4.31ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 45.691, "width_percent": 0.18}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.1043, "duration": 0.00373, "duration_str": "3.73ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 45.871, "width_percent": 0.156}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.1084151, "duration": 0.00368, "duration_str": "3.68ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 46.026, "width_percent": 0.154}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.112658, "duration": 0.00375, "duration_str": "3.75ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 46.18, "width_percent": 0.157}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.116939, "duration": 0.0038, "duration_str": "3.8ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 46.336, "width_percent": 0.159}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.121259, "duration": 0.00365, "duration_str": "3.65ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 46.495, "width_percent": 0.152}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.12549, "duration": 0.00391, "duration_str": "3.91ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 46.647, "width_percent": 0.163}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.130002, "duration": 0.00452, "duration_str": "4.52ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 46.81, "width_percent": 0.189}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.1351252, "duration": 0.0044800000000000005, "duration_str": "4.48ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 46.999, "width_percent": 0.187}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.140049, "duration": 0.0037400000000000003, "duration_str": "3.74ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 47.186, "width_percent": 0.156}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.144553, "duration": 0.0038599999999999997, "duration_str": "3.86ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 47.342, "width_percent": 0.161}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.149093, "duration": 0.00392, "duration_str": "3.92ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 47.503, "width_percent": 0.164}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.1536238, "duration": 0.00412, "duration_str": "4.12ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 47.667, "width_percent": 0.172}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.158173, "duration": 0.00368, "duration_str": "3.68ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 47.839, "width_percent": 0.154}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.16224, "duration": 0.00408, "duration_str": "4.08ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 47.992, "width_percent": 0.17}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.1668382, "duration": 0.005719999999999999, "duration_str": "5.72ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 48.163, "width_percent": 0.239}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.1730368, "duration": 0.00428, "duration_str": "4.28ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 48.401, "width_percent": 0.179}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.1776829, "duration": 0.0035600000000000002, "duration_str": "3.56ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 48.58, "width_percent": 0.149}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.181784, "duration": 0.0040999999999999995, "duration_str": "4.1ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 48.729, "width_percent": 0.171}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.18642, "duration": 0.00392, "duration_str": "3.92ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 48.9, "width_percent": 0.164}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.190773, "duration": 0.00365, "duration_str": "3.65ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 49.063, "width_percent": 0.152}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.194814, "duration": 0.0038, "duration_str": "3.8ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 49.216, "width_percent": 0.159}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.1990812, "duration": 0.00396, "duration_str": "3.96ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 49.374, "width_percent": 0.165}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.203425, "duration": 0.00383, "duration_str": "3.83ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 49.539, "width_percent": 0.16}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.20766, "duration": 0.00367, "duration_str": "3.67ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 49.699, "width_percent": 0.153}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.211911, "duration": 0.0039900000000000005, "duration_str": "3.99ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 49.852, "width_percent": 0.167}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.216482, "duration": 0.00391, "duration_str": "3.91ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 50.019, "width_percent": 0.163}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.220747, "duration": 0.00366, "duration_str": "3.66ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 50.182, "width_percent": 0.153}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.22474, "duration": 0.00348, "duration_str": "3.48ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 50.335, "width_percent": 0.145}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.2285872, "duration": 0.00363, "duration_str": "3.63ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 50.48, "width_percent": 0.151}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.232597, "duration": 0.00366, "duration_str": "3.66ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 50.632, "width_percent": 0.153}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.236626, "duration": 0.0037400000000000003, "duration_str": "3.74ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 50.784, "width_percent": 0.156}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.240726, "duration": 0.0034500000000000004, "duration_str": "3.45ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 50.94, "width_percent": 0.144}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.244591, "duration": 0.0035600000000000002, "duration_str": "3.56ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 51.084, "width_percent": 0.149}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.248558, "duration": 0.0038599999999999997, "duration_str": "3.86ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 51.233, "width_percent": 0.161}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.2527812, "duration": 0.00364, "duration_str": "3.64ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 51.394, "width_percent": 0.152}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.256794, "duration": 0.00359, "duration_str": "3.59ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 51.546, "width_percent": 0.15}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.260774, "duration": 0.00359, "duration_str": "3.59ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 51.696, "width_percent": 0.15}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.2649522, "duration": 0.0039900000000000005, "duration_str": "3.99ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 51.846, "width_percent": 0.167}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.269326, "duration": 0.00368, "duration_str": "3.68ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 52.012, "width_percent": 0.154}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.273381, "duration": 0.00368, "duration_str": "3.68ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 52.166, "width_percent": 0.154}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.27747, "duration": 0.0035, "duration_str": "3.5ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 52.319, "width_percent": 0.146}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.281611, "duration": 0.00404, "duration_str": "4.04ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 52.465, "width_percent": 0.169}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.286011, "duration": 0.00357, "duration_str": "3.57ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 52.634, "width_percent": 0.149}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.290215, "duration": 0.0061200000000000004, "duration_str": "6.12ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 52.783, "width_percent": 0.255}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.296916, "duration": 0.0038399999999999997, "duration_str": "3.84ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 53.038, "width_percent": 0.16}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.301212, "duration": 0.0038900000000000002, "duration_str": "3.89ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 53.199, "width_percent": 0.162}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.305469, "duration": 0.0038399999999999997, "duration_str": "3.84ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 53.361, "width_percent": 0.16}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.309732, "duration": 0.00368, "duration_str": "3.68ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 53.521, "width_percent": 0.154}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.313832, "duration": 0.00382, "duration_str": "3.82ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 53.675, "width_percent": 0.159}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.3181849, "duration": 0.00412, "duration_str": "4.12ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 53.834, "width_percent": 0.172}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.3227732, "duration": 0.00396, "duration_str": "3.96ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 54.006, "width_percent": 0.165}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.327141, "duration": 0.0035, "duration_str": "3.5ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 54.172, "width_percent": 0.146}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.331197, "duration": 0.004, "duration_str": "4ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 54.318, "width_percent": 0.167}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.335627, "duration": 0.00382, "duration_str": "3.82ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 54.485, "width_percent": 0.159}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.339831, "duration": 0.00361, "duration_str": "3.61ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 54.644, "width_percent": 0.151}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.343822, "duration": 0.0035099999999999997, "duration_str": "3.51ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 54.795, "width_percent": 0.146}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.347846, "duration": 0.03121, "duration_str": "31.21ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 54.941, "width_percent": 1.303}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.379555, "duration": 0.00369, "duration_str": "3.69ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 56.244, "width_percent": 0.154}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.38396, "duration": 0.00411, "duration_str": "4.11ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 56.398, "width_percent": 0.172}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.388454, "duration": 0.00359, "duration_str": "3.59ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 56.569, "width_percent": 0.15}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.3924582, "duration": 0.00361, "duration_str": "3.61ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 56.719, "width_percent": 0.151}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.396715, "duration": 0.00383, "duration_str": "3.83ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 56.87, "width_percent": 0.16}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.401017, "duration": 0.004070000000000001, "duration_str": "4.07ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 57.029, "width_percent": 0.17}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.405451, "duration": 0.00369, "duration_str": "3.69ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 57.199, "width_percent": 0.154}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.4095562, "duration": 0.00363, "duration_str": "3.63ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 57.353, "width_percent": 0.151}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.4138389, "duration": 0.004059999999999999, "duration_str": "4.06ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 57.505, "width_percent": 0.169}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.418284, "duration": 0.0037, "duration_str": "3.7ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 57.674, "width_percent": 0.154}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.422349, "duration": 0.00366, "duration_str": "3.66ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 57.829, "width_percent": 0.153}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.426403, "duration": 0.00398, "duration_str": "3.98ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 57.981, "width_percent": 0.166}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.4310021, "duration": 0.00396, "duration_str": "3.96ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 58.148, "width_percent": 0.165}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.435538, "duration": 0.0038, "duration_str": "3.8ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 58.313, "width_percent": 0.159}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.439805, "duration": 0.00373, "duration_str": "3.73ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 58.471, "width_percent": 0.156}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.4440908, "duration": 0.0035800000000000003, "duration_str": "3.58ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 58.627, "width_percent": 0.149}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.448115, "duration": 0.0039900000000000005, "duration_str": "3.99ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 58.776, "width_percent": 0.167}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.452512, "duration": 0.00378, "duration_str": "3.78ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 58.943, "width_percent": 0.158}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.4566882, "duration": 0.00396, "duration_str": "3.96ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 59.101, "width_percent": 0.165}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.460998, "duration": 0.00363, "duration_str": "3.63ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 59.266, "width_percent": 0.151}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.4651132, "duration": 0.00415, "duration_str": "4.15ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 59.417, "width_percent": 0.173}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.469682, "duration": 0.00385, "duration_str": "3.85ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 59.591, "width_percent": 0.161}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.473926, "duration": 0.00366, "duration_str": "3.66ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 59.751, "width_percent": 0.153}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.477957, "duration": 0.0036, "duration_str": "3.6ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 59.904, "width_percent": 0.15}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.481981, "duration": 0.00396, "duration_str": "3.96ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 60.054, "width_percent": 0.165}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.4863758, "duration": 0.00394, "duration_str": "3.94ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 60.22, "width_percent": 0.164}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.490681, "duration": 0.00372, "duration_str": "3.72ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 60.384, "width_percent": 0.155}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.494816, "duration": 0.00424, "duration_str": "4.24ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 60.539, "width_percent": 0.177}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.499521, "duration": 0.0038900000000000002, "duration_str": "3.89ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 60.716, "width_percent": 0.162}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.503809, "duration": 0.00377, "duration_str": "3.77ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 60.879, "width_percent": 0.157}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.507954, "duration": 0.02027, "duration_str": "20.27ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 61.036, "width_percent": 0.846}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.5285978, "duration": 0.0035800000000000003, "duration_str": "3.58ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 61.882, "width_percent": 0.149}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.532629, "duration": 0.00398, "duration_str": "3.98ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 62.031, "width_percent": 0.166}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.537105, "duration": 0.00369, "duration_str": "3.69ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 62.197, "width_percent": 0.154}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.541166, "duration": 0.0036, "duration_str": "3.6ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 62.351, "width_percent": 0.15}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.545192, "duration": 0.00392, "duration_str": "3.92ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 62.502, "width_percent": 0.164}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.549681, "duration": 0.004, "duration_str": "4ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 62.665, "width_percent": 0.167}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.554184, "duration": 0.00395, "duration_str": "3.95ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 62.832, "width_percent": 0.165}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.558521, "duration": 0.00364, "duration_str": "3.64ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 62.997, "width_percent": 0.152}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.562524, "duration": 0.0037, "duration_str": "3.7ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 63.149, "width_percent": 0.154}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.566602, "duration": 0.0038, "duration_str": "3.8ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 63.303, "width_percent": 0.159}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.570774, "duration": 0.00369, "duration_str": "3.69ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 63.462, "width_percent": 0.154}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.5748332, "duration": 0.00363, "duration_str": "3.63ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 63.616, "width_percent": 0.151}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.578835, "duration": 0.00366, "duration_str": "3.66ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 63.767, "width_percent": 0.153}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.582862, "duration": 0.01266, "duration_str": "12.66ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 63.92, "width_percent": 0.528}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.595881, "duration": 0.00362, "duration_str": "3.62ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 64.449, "width_percent": 0.151}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.599861, "duration": 0.00387, "duration_str": "3.87ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 64.6, "width_percent": 0.162}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.6041691, "duration": 0.00378, "duration_str": "3.78ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 64.761, "width_percent": 0.158}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.608319, "duration": 0.00364, "duration_str": "3.64ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 64.919, "width_percent": 0.152}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.612328, "duration": 0.0035299999999999997, "duration_str": "3.53ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 65.071, "width_percent": 0.147}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.616223, "duration": 0.0107, "duration_str": "10.7ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 65.218, "width_percent": 0.447}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.627314, "duration": 0.00362, "duration_str": "3.62ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 65.665, "width_percent": 0.151}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.631532, "duration": 0.00395, "duration_str": "3.95ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 65.816, "width_percent": 0.165}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.6358979, "duration": 0.00376, "duration_str": "3.76ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 65.981, "width_percent": 0.157}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.640052, "duration": 0.00366, "duration_str": "3.66ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 66.137, "width_percent": 0.153}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.644056, "duration": 0.00349, "duration_str": "3.49ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 66.29, "width_percent": 0.146}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.648043, "duration": 0.004059999999999999, "duration_str": "4.06ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 66.436, "width_percent": 0.169}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.6524992, "duration": 0.0038, "duration_str": "3.8ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 66.605, "width_percent": 0.159}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.656696, "duration": 0.016649999999999998, "duration_str": "16.65ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 66.764, "width_percent": 0.695}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.673732, "duration": 0.00363, "duration_str": "3.63ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 67.459, "width_percent": 0.151}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.677752, "duration": 0.0035299999999999997, "duration_str": "3.53ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 67.61, "width_percent": 0.147}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.6818268, "duration": 0.00394, "duration_str": "3.94ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 67.758, "width_percent": 0.164}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.686215, "duration": 0.00367, "duration_str": "3.67ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 67.922, "width_percent": 0.153}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.690277, "duration": 0.00361, "duration_str": "3.61ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 68.075, "width_percent": 0.151}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.69431, "duration": 0.0035800000000000003, "duration_str": "3.58ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 68.226, "width_percent": 0.149}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.698432, "duration": 0.0038599999999999997, "duration_str": "3.86ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 68.375, "width_percent": 0.161}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.702722, "duration": 0.00368, "duration_str": "3.68ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 68.536, "width_percent": 0.154}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.7067819, "duration": 0.00372, "duration_str": "3.72ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 68.69, "width_percent": 0.155}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.710893, "duration": 0.00359, "duration_str": "3.59ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 68.845, "width_percent": 0.15}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.715144, "duration": 0.00438, "duration_str": "4.38ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 68.995, "width_percent": 0.183}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.71988, "duration": 0.00366, "duration_str": "3.66ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 69.178, "width_percent": 0.153}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.7239132, "duration": 0.0036, "duration_str": "3.6ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 69.331, "width_percent": 0.15}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.7278738, "duration": 0.0035800000000000003, "duration_str": "3.58ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 69.481, "width_percent": 0.149}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.7318711, "duration": 0.0039900000000000005, "duration_str": "3.99ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 69.63, "width_percent": 0.167}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.736258, "duration": 0.00357, "duration_str": "3.57ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 69.797, "width_percent": 0.149}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.740205, "duration": 0.00367, "duration_str": "3.67ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 69.946, "width_percent": 0.153}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.744264, "duration": 0.00352, "duration_str": "3.52ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 70.099, "width_percent": 0.147}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.748208, "duration": 0.00381, "duration_str": "3.81ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 70.246, "width_percent": 0.159}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.7524688, "duration": 0.00376, "duration_str": "3.76ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 70.405, "width_percent": 0.157}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.756611, "duration": 0.0036, "duration_str": "3.6ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 70.562, "width_percent": 0.15}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.760595, "duration": 0.0035299999999999997, "duration_str": "3.53ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 70.712, "width_percent": 0.147}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.7646458, "duration": 0.00376, "duration_str": "3.76ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 70.859, "width_percent": 0.157}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.768777, "duration": 0.00394, "duration_str": "3.94ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 71.016, "width_percent": 0.164}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.77311, "duration": 0.00385, "duration_str": "3.85ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 71.181, "width_percent": 0.161}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.7773209, "duration": 0.00354, "duration_str": "3.54ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 71.341, "width_percent": 0.148}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.781433, "duration": 0.00379, "duration_str": "3.79ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 71.489, "width_percent": 0.158}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.785601, "duration": 0.00368, "duration_str": "3.68ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 71.647, "width_percent": 0.154}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.789673, "duration": 0.00383, "duration_str": "3.83ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 71.801, "width_percent": 0.16}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.793843, "duration": 0.00349, "duration_str": "3.49ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 71.961, "width_percent": 0.146}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.7978, "duration": 0.004030000000000001, "duration_str": "4.03ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 72.106, "width_percent": 0.168}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.802324, "duration": 0.00378, "duration_str": "3.78ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 72.274, "width_percent": 0.158}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.806501, "duration": 0.0038799999999999998, "duration_str": "3.88ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 72.432, "width_percent": 0.162}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.810771, "duration": 0.00364, "duration_str": "3.64ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 72.594, "width_percent": 0.152}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.815019, "duration": 0.00393, "duration_str": "3.93ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 72.746, "width_percent": 0.164}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.8194509, "duration": 0.00376, "duration_str": "3.76ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 72.91, "width_percent": 0.157}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.823607, "duration": 0.0037099999999999998, "duration_str": "3.71ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 73.067, "width_percent": 0.155}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.827676, "duration": 0.00361, "duration_str": "3.61ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 73.222, "width_percent": 0.151}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.8316982, "duration": 0.00379, "duration_str": "3.79ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 73.372, "width_percent": 0.158}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.835936, "duration": 0.0038399999999999997, "duration_str": "3.84ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 73.531, "width_percent": 0.16}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.840157, "duration": 0.00367, "duration_str": "3.67ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 73.691, "width_percent": 0.153}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.844182, "duration": 0.00354, "duration_str": "3.54ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 73.844, "width_percent": 0.148}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.848222, "duration": 0.00443, "duration_str": "4.43ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 73.992, "width_percent": 0.185}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.853079, "duration": 0.00364, "duration_str": "3.64ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 74.177, "width_percent": 0.152}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.857111, "duration": 0.0035800000000000003, "duration_str": "3.58ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 74.329, "width_percent": 0.149}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.861094, "duration": 0.00354, "duration_str": "3.54ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 74.478, "width_percent": 0.148}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.865339, "duration": 0.0041600000000000005, "duration_str": "4.16ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 74.626, "width_percent": 0.174}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.869975, "duration": 0.00367, "duration_str": "3.67ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 74.799, "width_percent": 0.153}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.8740149, "duration": 0.00375, "duration_str": "3.75ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 74.953, "width_percent": 0.157}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.878181, "duration": 0.0037, "duration_str": "3.7ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 75.109, "width_percent": 0.154}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.882544, "duration": 0.01688, "duration_str": "16.88ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 75.263, "width_percent": 0.704}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.899946, "duration": 0.00383, "duration_str": "3.83ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 75.968, "width_percent": 0.16}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.9042, "duration": 0.00363, "duration_str": "3.63ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 76.128, "width_percent": 0.151}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.908186, "duration": 0.0037, "duration_str": "3.7ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 76.279, "width_percent": 0.154}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.912278, "duration": 0.00368, "duration_str": "3.68ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 76.434, "width_percent": 0.154}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.9163492, "duration": 0.0036, "duration_str": "3.6ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 76.587, "width_percent": 0.15}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.920331, "duration": 0.0037400000000000003, "duration_str": "3.74ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 76.737, "width_percent": 0.156}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.9244392, "duration": 0.00361, "duration_str": "3.61ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 76.894, "width_percent": 0.151}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.928405, "duration": 0.0035600000000000002, "duration_str": "3.56ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 77.044, "width_percent": 0.149}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.932328, "duration": 0.0037400000000000003, "duration_str": "3.74ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 77.193, "width_percent": 0.156}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.93646, "duration": 0.00367, "duration_str": "3.67ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 77.349, "width_percent": 0.153}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.9405098, "duration": 0.0035800000000000003, "duration_str": "3.58ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 77.502, "width_percent": 0.149}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.944449, "duration": 0.00349, "duration_str": "3.49ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 77.651, "width_percent": 0.146}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.948553, "duration": 0.00405, "duration_str": "4.05ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 77.797, "width_percent": 0.169}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.953112, "duration": 0.00375, "duration_str": "3.75ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 77.966, "width_percent": 0.157}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.957248, "duration": 0.00365, "duration_str": "3.65ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 78.123, "width_percent": 0.152}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.961298, "duration": 0.00361, "duration_str": "3.61ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 78.275, "width_percent": 0.151}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.965298, "duration": 0.00383, "duration_str": "3.83ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 78.426, "width_percent": 0.16}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.969529, "duration": 0.00376, "duration_str": "3.76ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 78.585, "width_percent": 0.157}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.973686, "duration": 0.00395, "duration_str": "3.95ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 78.742, "width_percent": 0.165}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.97805, "duration": 0.00354, "duration_str": "3.54ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 78.907, "width_percent": 0.148}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.982171, "duration": 0.004200000000000001, "duration_str": "4.2ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 79.055, "width_percent": 0.175}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.986834, "duration": 0.004019999999999999, "duration_str": "4.02ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 79.23, "width_percent": 0.168}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038459.991338, "duration": 0.015099999999999999, "duration_str": "15.1ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 79.398, "width_percent": 0.63}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038460.0068018, "duration": 0.00395, "duration_str": "3.95ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 80.028, "width_percent": 0.165}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038460.011208, "duration": 0.00383, "duration_str": "3.83ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 80.193, "width_percent": 0.16}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038460.0155668, "duration": 0.00408, "duration_str": "4.08ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 80.353, "width_percent": 0.17}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038460.0201159, "duration": 0.00394, "duration_str": "3.94ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 80.523, "width_percent": 0.164}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038460.0244591, "duration": 0.00435, "duration_str": "4.35ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 80.688, "width_percent": 0.182}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038460.02943, "duration": 0.0052699999999999995, "duration_str": "5.27ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 80.869, "width_percent": 0.22}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038460.0352552, "duration": 0.00501, "duration_str": "5.01ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 81.089, "width_percent": 0.209}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038460.040763, "duration": 0.0059900000000000005, "duration_str": "5.99ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 81.298, "width_percent": 0.25}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038460.047387, "duration": 0.00552, "duration_str": "5.52ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 81.548, "width_percent": 0.23}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038460.053419, "duration": 0.0041600000000000005, "duration_str": "4.16ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 81.779, "width_percent": 0.174}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038460.058039, "duration": 0.0038399999999999997, "duration_str": "3.84ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 81.952, "width_percent": 0.16}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038460.0624151, "duration": 0.00363, "duration_str": "3.63ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 82.112, "width_percent": 0.151}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038460.066641, "duration": 0.0038599999999999997, "duration_str": "3.86ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 82.264, "width_percent": 0.161}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038460.070946, "duration": 0.0038, "duration_str": "3.8ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 82.425, "width_percent": 0.159}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038460.075166, "duration": 0.00375, "duration_str": "3.75ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 82.584, "width_percent": 0.157}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038460.079435, "duration": 0.00366, "duration_str": "3.66ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 82.74, "width_percent": 0.153}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038460.083553, "duration": 0.00379, "duration_str": "3.79ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 82.893, "width_percent": 0.158}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038460.087739, "duration": 0.0038799999999999998, "duration_str": "3.88ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 83.051, "width_percent": 0.162}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038460.0920181, "duration": 0.01328, "duration_str": "13.28ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 83.213, "width_percent": 0.554}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038460.1056302, "duration": 0.00364, "duration_str": "3.64ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 83.767, "width_percent": 0.152}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038460.109633, "duration": 0.0034, "duration_str": "3.4ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 83.919, "width_percent": 0.142}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038460.113516, "duration": 0.0035, "duration_str": "3.5ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 84.061, "width_percent": 0.146}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038460.117575, "duration": 0.00453, "duration_str": "4.53ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 84.207, "width_percent": 0.189}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038460.12256, "duration": 0.00369, "duration_str": "3.69ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 84.396, "width_percent": 0.154}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038460.1266491, "duration": 0.00327, "duration_str": "3.27ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 84.55, "width_percent": 0.136}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038460.1302319, "duration": 0.00359, "duration_str": "3.59ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 84.687, "width_percent": 0.15}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038460.1342142, "duration": 0.00382, "duration_str": "3.82ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 84.836, "width_percent": 0.159}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038460.138422, "duration": 0.0037400000000000003, "duration_str": "3.74ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 84.996, "width_percent": 0.156}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038460.142582, "duration": 0.00372, "duration_str": "3.72ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 85.152, "width_percent": 0.155}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038460.146782, "duration": 0.00377, "duration_str": "3.77ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 85.307, "width_percent": 0.157}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038460.1512241, "duration": 0.00431, "duration_str": "4.31ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 85.464, "width_percent": 0.18}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038460.155916, "duration": 0.00372, "duration_str": "3.72ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 85.644, "width_percent": 0.155}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038460.160138, "duration": 0.00376, "duration_str": "3.76ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 85.8, "width_percent": 0.157}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038460.164624, "duration": 0.00377, "duration_str": "3.77ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 85.957, "width_percent": 0.157}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038460.169254, "duration": 0.00381, "duration_str": "3.81ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 86.114, "width_percent": 0.159}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038460.173474, "duration": 0.0037099999999999998, "duration_str": "3.71ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 86.273, "width_percent": 0.155}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038460.1777072, "duration": 0.00363, "duration_str": "3.63ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 86.428, "width_percent": 0.151}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038460.1819, "duration": 0.0038399999999999997, "duration_str": "3.84ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 86.579, "width_percent": 0.16}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038460.186385, "duration": 0.01056, "duration_str": "10.56ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 86.739, "width_percent": 0.441}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038460.197552, "duration": 0.004030000000000001, "duration_str": "4.03ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 87.18, "width_percent": 0.168}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038460.202042, "duration": 0.00363, "duration_str": "3.63ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 87.348, "width_percent": 0.151}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038460.206033, "duration": 0.0035800000000000003, "duration_str": "3.58ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 87.5, "width_percent": 0.149}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038460.21001, "duration": 0.00369, "duration_str": "3.69ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 87.649, "width_percent": 0.154}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038460.214292, "duration": 0.00408, "duration_str": "4.08ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 87.803, "width_percent": 0.17}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038460.219214, "duration": 0.00481, "duration_str": "4.81ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 87.974, "width_percent": 0.201}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038460.2246861, "duration": 0.004, "duration_str": "4ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 88.174, "width_percent": 0.167}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038460.22911, "duration": 0.00391, "duration_str": "3.91ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 88.341, "width_percent": 0.163}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038460.233512, "duration": 0.00369, "duration_str": "3.69ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 88.504, "width_percent": 0.154}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038460.237615, "duration": 0.00365, "duration_str": "3.65ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 88.658, "width_percent": 0.152}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038460.24162, "duration": 0.0035499999999999998, "duration_str": "3.55ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 88.811, "width_percent": 0.148}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038460.245583, "duration": 0.00363, "duration_str": "3.63ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 88.959, "width_percent": 0.151}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038460.249655, "duration": 0.0157, "duration_str": "15.7ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 89.11, "width_percent": 0.655}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038460.265931, "duration": 0.00375, "duration_str": "3.75ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 89.766, "width_percent": 0.157}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038460.2700288, "duration": 0.00373, "duration_str": "3.73ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 89.922, "width_percent": 0.156}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038460.274131, "duration": 0.00368, "duration_str": "3.68ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 90.078, "width_percent": 0.154}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038460.278202, "duration": 0.0036, "duration_str": "3.6ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 90.231, "width_percent": 0.15}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038460.282393, "duration": 0.00419, "duration_str": "4.19ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 90.382, "width_percent": 0.175}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038460.2870011, "duration": 0.0038, "duration_str": "3.8ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 90.556, "width_percent": 0.159}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038460.2911801, "duration": 0.00378, "duration_str": "3.78ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 90.715, "width_percent": 0.158}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038460.295331, "duration": 0.00381, "duration_str": "3.81ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 90.873, "width_percent": 0.159}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038460.299651, "duration": 0.004019999999999999, "duration_str": "4.02ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 91.032, "width_percent": 0.168}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038460.304113, "duration": 0.0037, "duration_str": "3.7ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 91.2, "width_percent": 0.154}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038460.3081832, "duration": 0.00379, "duration_str": "3.79ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 91.354, "width_percent": 0.158}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038460.3123672, "duration": 0.00362, "duration_str": "3.62ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 91.512, "width_percent": 0.151}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038460.316439, "duration": 0.00387, "duration_str": "3.87ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 91.663, "width_percent": 0.162}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038460.320722, "duration": 0.00383, "duration_str": "3.83ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 91.825, "width_percent": 0.16}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038460.32494, "duration": 0.0039900000000000005, "duration_str": "3.99ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 91.985, "width_percent": 0.167}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038460.329343, "duration": 0.004, "duration_str": "4ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 92.151, "width_percent": 0.167}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038460.333804, "duration": 0.00405, "duration_str": "4.05ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 92.318, "width_percent": 0.169}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038460.338236, "duration": 0.00387, "duration_str": "3.87ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 92.487, "width_percent": 0.162}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038460.34249, "duration": 0.00381, "duration_str": "3.81ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 92.649, "width_percent": 0.159}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038460.346694, "duration": 0.00368, "duration_str": "3.68ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 92.808, "width_percent": 0.154}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038460.350848, "duration": 0.0039900000000000005, "duration_str": "3.99ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 92.961, "width_percent": 0.167}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038460.3552022, "duration": 0.00396, "duration_str": "3.96ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 93.128, "width_percent": 0.165}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038460.35951, "duration": 0.00349, "duration_str": "3.49ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 93.293, "width_percent": 0.146}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038460.3632948, "duration": 0.015179999999999999, "duration_str": "15.18ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 93.439, "width_percent": 0.634}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038460.378994, "duration": 0.0038799999999999998, "duration_str": "3.88ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 94.072, "width_percent": 0.162}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038460.3833342, "duration": 0.00415, "duration_str": "4.15ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 94.234, "width_percent": 0.173}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038460.387861, "duration": 0.00391, "duration_str": "3.91ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 94.407, "width_percent": 0.163}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038460.392141, "duration": 0.00369, "duration_str": "3.69ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 94.57, "width_percent": 0.154}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038460.396183, "duration": 0.00376, "duration_str": "3.76ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 94.724, "width_percent": 0.157}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038460.400317, "duration": 0.00377, "duration_str": "3.77ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 94.881, "width_percent": 0.157}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038460.404526, "duration": 0.00362, "duration_str": "3.62ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 95.039, "width_percent": 0.151}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038460.408528, "duration": 0.00366, "duration_str": "3.66ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 95.19, "width_percent": 0.153}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038460.4125612, "duration": 0.00852, "duration_str": "8.52ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 95.342, "width_percent": 0.356}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038460.421525, "duration": 0.00889, "duration_str": "8.89ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 95.698, "width_percent": 0.371}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038460.430935, "duration": 0.00385, "duration_str": "3.85ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 96.069, "width_percent": 0.161}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038460.4351828, "duration": 0.005679999999999999, "duration_str": "5.68ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 96.23, "width_percent": 0.237}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038460.44127, "duration": 0.013470000000000001, "duration_str": "13.47ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 96.467, "width_percent": 0.562}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038460.45513, "duration": 0.00367, "duration_str": "3.67ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 97.029, "width_percent": 0.153}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038460.459178, "duration": 0.00354, "duration_str": "3.54ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 97.182, "width_percent": 0.148}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038460.463088, "duration": 0.01701, "duration_str": "17.01ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 97.33, "width_percent": 0.71}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038460.480545, "duration": 0.00382, "duration_str": "3.82ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 98.04, "width_percent": 0.159}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038460.484774, "duration": 0.0038399999999999997, "duration_str": "3.84ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 98.199, "width_percent": 0.16}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038460.4890192, "duration": 0.00365, "duration_str": "3.65ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 98.359, "width_percent": 0.152}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038460.493022, "duration": 0.00365, "duration_str": "3.65ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 98.512, "width_percent": 0.152}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038460.497014, "duration": 0.00344, "duration_str": "3.44ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 98.664, "width_percent": 0.144}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038460.500875, "duration": 0.0038, "duration_str": "3.8ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 98.808, "width_percent": 0.159}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038460.505074, "duration": 0.00373, "duration_str": "3.73ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 98.966, "width_percent": 0.156}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038460.509239, "duration": 0.0038799999999999998, "duration_str": "3.88ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 99.122, "width_percent": 0.162}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038460.5136158, "duration": 0.00376, "duration_str": "3.76ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 99.284, "width_percent": 0.157}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038460.517923, "duration": 0.004019999999999999, "duration_str": "4.02ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 99.441, "width_percent": 0.168}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038460.522283, "duration": 0.0039700000000000004, "duration_str": "3.97ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 99.609, "width_percent": 0.166}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1751038460.526842, "duration": 0.00541, "duration_str": "5.41ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 99.774, "width_percent": 0.226}, {"sql": "... 343 additional queries are executed but now shown because of Debugbar query limits. Limits can be raised in the config (debugbar.options.db.soft/hard_limit)", "type": "info"}]}, "models": {"data": {"Xmetr\\ACL\\Models\\User": {"value": 1, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Facl%2Fsrc%2FModels%2FUser.php:1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "https://xmetr.gc/admin/plugins/status?name=gone-guard", "action_name": "plugins.change.status", "controller_action": "Xmetr\\PluginManagement\\Http\\Controllers\\PluginManagementController@update", "uri": "PUT admin/plugins/status", "permission": "plugins.index", "controller": "Xmetr\\PluginManagement\\Http\\Controllers\\PluginManagementController@update<a href=\"vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fpackages%2Fplugin-management%2Fsrc%2FHttp%2FControllers%2FPluginManagementController.php:93\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "Xmetr\\PluginManagement\\Http\\Controllers", "prefix": "admin/plugins", "file": "<a href=\"vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fpackages%2Fplugin-management%2Fsrc%2FHttp%2FControllers%2FPluginManagementController.php:93\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php:93-130</a>", "middleware": "web, core, auth, preventDemo", "duration": "6.03s", "peak_memory": "48MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-88555716 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"10 characters\">gone-guard</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-88555716\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-731389108 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_method</span>\" => \"<span class=sf-dump-str title=\"3 characters\">put</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-731389108\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2117740637 data-indent-pad=\"  \"><span class=sf-dump-note>array:20</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">xmetr.gc</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">17</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-xsrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6Ik96cWxDd0JWaENacmZlZThxVllOK2c9PSIsInZhbHVlIjoiMjdSMjduTDBRbmNVTUVVSFhjbDF0VW95cEQzNmMrV3p5TTJsbDZoZEptOE5HaDhNMENzalNaREd5eEMxZ1N3NnY4cTRVY1Z1VzhId2hTaDQrYVY0MHlsRTloc2VmRCtscFBlZ1VXL2hnbXE4TGtaSXV1L2RZOUhQdXFUakRBaDciLCJtYWMiOiIwNGI2ZmM4MDg3ODUzMmU3NjljOTZkZTI3Y2I0ZDA1NGNjN2UxMGI0NGFiMmM5MGZjNDk1YzZhZDNjMTE4YmM2IiwidGFnIjoiIn0=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/json, text/plain, */*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">https://xmetr.gc</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">https://xmetr.gc/admin/plugins/installed</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"50 characters\">en-US,en;q=0.9,ur;q=0.8,pl;q=0.7,ru;q=0.6,ar;q=0.5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1687 characters\">remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IjE5eVFoNXI3S3dCZTVpWVUvb1ZlTkE9PSIsInZhbHVlIjoiVFFEOHg1MHRNUCtIQndDL1FUdThDSVJxSzFQTTlRRWNjUGJtalZRTmpvRFQrQ1UybGVrWnR6dUN0ZU16VVJXRWg0UVg5cFdaQmlmL212RUdPSjJLcUgray8wa3JyeFU3LzUrRUs3by9jVXM1cCt0MDZCTXZjczI2RTRNR0xqeTBJT1dpcWNNQTdEeW8wYkZ1S3lUc1pZSVFCd2FiN2EvdTRmZGdiU2FOYVREVUtPdGw5SG1xOUZHVU5Db0Zwa1lvVTRXRDFPOXBhNnZOQ3hkWjJjU2JtaDRQRU9WYXRwczhPTmM1Wit2aThBcz0iLCJtYWMiOiI0NThmZWI3NDI2NThmMTIxOWRlN2JiMjljY2Y3MjQzMDUzNmQyYjA5NzllZTg4YmY2ZTc5MGExYjEzODJhMGU1IiwidGFnIjoiIn0%3D; _ga=GA1.1.2064844405.1743885089; cookie_for_consent=1; wishlist=657; _hjSessionUser_6417422=eyJpZCI6IjhkMzA2ODc1LTcwNjctNTU3Yi1hNDljLTdhNmEyNTdkM2QxOCIsImNyZWF0ZWQiOjE3NDgzODAwMDM5NTcsImV4aXN0aW5nIjp0cnVlfQ==; _hjSession_6417422=eyJpZCI6ImVlNDZjNjhjLTVjNjEtNGVlOS1hMjI0LTRmMDZjNmU3ZGY4NCIsImMiOjE3NTEwMzQ2NzE1ODcsInMiOjEsInIiOjAsInNiIjowLCJzciI6MCwic2UiOjAsImZzIjowfQ==; _ga_KQ6X76DET4=GS2.1.s1751034674$o76$g1$t1751038392$j36$l0$h0; XSRF-TOKEN=eyJpdiI6Ik96cWxDd0JWaENacmZlZThxVllOK2c9PSIsInZhbHVlIjoiMjdSMjduTDBRbmNVTUVVSFhjbDF0VW95cEQzNmMrV3p5TTJsbDZoZEptOE5HaDhNMENzalNaREd5eEMxZ1N3NnY4cTRVY1Z1VzhId2hTaDQrYVY0MHlsRTloc2VmRCtscFBlZ1VXL2hnbXE4TGtaSXV1L2RZOUhQdXFUakRBaDciLCJtYWMiOiIwNGI2ZmM4MDg3ODUzMmU3NjljOTZkZTI3Y2I0ZDA1NGNjN2UxMGI0NGFiMmM5MGZjNDk1YzZhZDNjMTE4YmM2IiwidGFnIjoiIn0%3D; xmetr_session=eyJpdiI6IkJwT2Y2MVRVSDJTMUg1aXk0MldFT0E9PSIsInZhbHVlIjoiMnZLcFczZmFJNzBWb1poUmpoNDJQZUh3RWxUQWZmNXhXRks1YVJreDVMbEhMQXVZa1FxNmE5RVhCdURqUXNLYmJ1OFRiZzZUb0VJU2NGWG9DMFJJNFcyMU9XbjYzQU5yK1JwUzVHeDBQTmtGNmNBYkF6ZkdQOWFHY0RZb0NNY0UiLCJtYWMiOiIzZjc3OTBhZmFmNDRjYWQzNzkyNDY0NzMxNjU0MTNiYTJhMzJhZjU5MzE2OWI1Yzc0NGYyZWE2YTk5YmQwZWQ5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2117740637\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-999094425 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">1|1uQwj4bI0uiYsJi0APplQif6Zji5Zq40vBOmafc1oj6NYK3vu89uqM3GLC7O|$2y$04$Astns3SXblBceX03zRicBuVKoyIRRFaOY1GuvREgnRYhHEelfNbdu</span>\"\n  \"<span class=sf-dump-key>_ga</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_for_consent</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>wishlist</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_hjSessionUser_6417422</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_hjSession_6417422</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_ga_KQ6X76DET4</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Gi5RJZ0ijqaVr6Lg3jFaXX5hF7p742ZKiCmqC77D</span>\"\n  \"<span class=sf-dump-key>xmetr_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">crfBFjn65dZVGczOyRKD4yPI4R8TEIsbnbtVi55O</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-999094425\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-809463737 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 15:34:22 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-809463737\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-696796553 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Gi5RJZ0ijqaVr6Lg3jFaXX5hF7p742ZKiCmqC77D</span>\"\n  \"<span class=sf-dump-key>language</span>\" => \"<span class=sf-dump-str title=\"2 characters\">ru</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>new</span>\" => []\n    \"<span class=sf-dump-key>old</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"40 characters\">https://xmetr.gc/admin/plugins/installed</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n  \"<span class=sf-dump-key>previous_language</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-696796553\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "https://xmetr.gc/admin/plugins/status?name=gone-guard", "action_name": "plugins.change.status", "controller_action": "Xmetr\\PluginManagement\\Http\\Controllers\\PluginManagementController@update"}, "badge": null}}