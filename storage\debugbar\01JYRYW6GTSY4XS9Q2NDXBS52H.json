{"__meta": {"id": "01JYRYW6GTSY4XS9Q2NDXBS52H", "datetime": "2025-06-27 15:07:40", "utime": **********.95511, "method": "GET", "uri": "/ru/ajax/properties/map?page=2", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751036858.844874, "end": **********.955129, "duration": 2.110255002975464, "duration_str": "2.11s", "measures": [{"label": "Booting", "start": 1751036858.844874, "relative_start": 0, "end": **********.919057, "relative_end": **********.919057, "duration": 1.****************, "duration_str": "1.07s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.919074, "relative_start": 1.**************, "end": **********.955131, "relative_end": 2.1457672119140625e-06, "duration": 1.****************, "duration_str": "1.04s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.941389, "relative_start": 1.**************, "end": **********.967216, "relative_end": **********.967216, "duration": 0.*****************, "duration_str": "25.83ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.112579, "relative_start": 1.****************, "end": **********.952276, "relative_end": **********.952276, "duration": 0.****************, "duration_str": "840ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "61MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "10.x", "tooltip": {"Laravel Version": "10.48.29", "PHP Version": "8.3.17", "Environment": "local", "Debug Mode": "Enabled", "URL": "xmetr.gc", "Timezone": "UTC", "Locale": "ru"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 32, "nb_statements": 32, "nb_visible_statements": 32, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.12720999999999996, "accumulated_duration_str": "127ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select count(*) as aggregate from `re_properties` where (`re_properties`.`moderation_status` = 'approved' and `re_properties`.`status` != 'not_available') and (`expire_date` >= '2025-06-27 15:07:39' or `never_expired` = 1) and `status` != 'rented'", "type": "query", "params": [], "bindings": ["approved", "not_available", "2025-06-27 15:07:39", 1, "rented"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "platform/core/support/src/Repositories/Eloquent/RepositoriesAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 366}, {"index": 17, "namespace": null, "name": "platform/plugins/real-estate/src/Repositories/Eloquent/PropertyRepository.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Repositories\\Eloquent\\PropertyRepository.php", "line": 479}, {"index": 18, "namespace": null, "name": "platform/themes/xmetr/src/Http/Controllers/XmetrController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\themes\\xmetr\\src\\Http\\Controllers\\XmetrController.php", "line": 84}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.999839, "duration": 0.0024500000000000004, "duration_str": "2.45ms", "memory": 0, "memory_str": null, "filename": "RepositoriesAbstract.php:366", "source": {"index": 16, "namespace": null, "name": "platform/core/support/src/Repositories/Eloquent/RepositoriesAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 366}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsupport%2Fsrc%2FRepositories%2FEloquent%2FRepositoriesAbstract.php:366", "ajax": false, "filename": "RepositoriesAbstract.php", "line": "366"}, "connection": "xmetr", "explain": null, "start_percent": 0, "width_percent": 1.926}, {"sql": "select * from `re_properties` where (`re_properties`.`moderation_status` = 'approved' and `re_properties`.`status` != 'not_available') and (`expire_date` >= '2025-06-27 15:07:39' or `never_expired` = 1) and `status` != 'rented' order by `created_at` desc limit 20 offset 20", "type": "query", "params": [], "bindings": ["approved", "not_available", "2025-06-27 15:07:39", 1, "rented"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/core/support/src/Repositories/Eloquent/RepositoriesAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 366}, {"index": 18, "namespace": null, "name": "platform/plugins/real-estate/src/Repositories/Eloquent/PropertyRepository.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Repositories\\Eloquent\\PropertyRepository.php", "line": 479}, {"index": 19, "namespace": null, "name": "platform/themes/xmetr/src/Http/Controllers/XmetrController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\themes\\xmetr\\src\\Http\\Controllers\\XmetrController.php", "line": 84}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.0033908, "duration": 0.00218, "duration_str": "2.18ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 1.926, "width_percent": 1.714}, {"sql": "select `id`, `key`, `prefix`, `reference_id` from `slugs` where `slugs`.`reference_id` in (1132, 1133, 1135, 1136, 1137, 1138, 1139, 1140, 1141, 1142, 1143, 1144, 1145, 1146, 1147, 1148, 1149, 1150, 1151, 1152) and `slugs`.`reference_type` = 'Xmetr\\\\RealEstate\\\\Models\\\\Property'", "type": "query", "params": [], "bindings": ["Xmetr\\RealEstate\\Models\\Property"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 23, "namespace": null, "name": "platform/core/support/src/Repositories/Eloquent/RepositoriesAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 366}, {"index": 24, "namespace": null, "name": "platform/plugins/real-estate/src/Repositories/Eloquent/PropertyRepository.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Repositories\\Eloquent\\PropertyRepository.php", "line": 479}, {"index": 25, "namespace": null, "name": "platform/themes/xmetr/src/Http/Controllers/XmetrController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\themes\\xmetr\\src\\Http\\Controllers\\XmetrController.php", "line": 84}], "start": **********.0108778, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 3.64, "width_percent": 0.448}, {"sql": "select * from `slugs_translations` where `slugs_translations`.`slugs_id` in (2808, 2809, 2810, 2811, 2813, 2814, 2815, 2816, 2817, 2818, 2819, 2820, 2821, 2822, 2823, 2824, 2825, 2826, 2827, 2828, 2829, 2830, 2831, 2832, 2833, 2834, 2835, 2836, 2837, 2838, 2839, 2840, 2841, 2842, 2843, 2844, 2845, 2846, 2847, 2848)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 23, "namespace": null, "name": "platform/plugins/language-advanced/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\language-advanced\\src\\Providers\\HookServiceProvider.php", "line": 55}, {"index": 27, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\helpers\\action-filter.php", "line": 39}, {"index": 34, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 36, "namespace": null, "name": "platform/core/support/src/Repositories/Eloquent/RepositoriesAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 366}], "start": **********.019341, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 4.088, "width_percent": 0.495}, {"sql": "select `id`, `name` from `states` where `states`.`id` in (193)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 23, "namespace": null, "name": "platform/core/support/src/Repositories/Eloquent/RepositoriesAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 366}, {"index": 24, "namespace": null, "name": "platform/plugins/real-estate/src/Repositories/Eloquent/PropertyRepository.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Repositories\\Eloquent\\PropertyRepository.php", "line": 479}, {"index": 25, "namespace": null, "name": "platform/themes/xmetr/src/Http/Controllers/XmetrController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\themes\\xmetr\\src\\Http\\Controllers\\XmetrController.php", "line": 84}], "start": **********.048307, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 4.583, "width_percent": 0.322}, {"sql": "select * from `states_translations` where `states_translations`.`states_id` in (193)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 23, "namespace": null, "name": "platform/plugins/language-advanced/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\language-advanced\\src\\Providers\\HookServiceProvider.php", "line": 55}, {"index": 27, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\helpers\\action-filter.php", "line": 39}, {"index": 34, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 36, "namespace": null, "name": "platform/core/support/src/Repositories/Eloquent/RepositoriesAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 366}], "start": **********.0507638, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 4.905, "width_percent": 0.314}, {"sql": "select `id`, `name` from `cities` where `cities`.`id` in (8189, 8204, 8205, 8212, 8265, 8297, 8316, 8336)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 23, "namespace": null, "name": "platform/core/support/src/Repositories/Eloquent/RepositoriesAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 366}, {"index": 24, "namespace": null, "name": "platform/plugins/real-estate/src/Repositories/Eloquent/PropertyRepository.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Repositories\\Eloquent\\PropertyRepository.php", "line": 479}, {"index": 25, "namespace": null, "name": "platform/themes/xmetr/src/Http/Controllers/XmetrController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\themes\\xmetr\\src\\Http\\Controllers\\XmetrController.php", "line": 84}], "start": **********.0542068, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 5.22, "width_percent": 0.307}, {"sql": "select * from `cities_translations` where `cities_translations`.`cities_id` in (8189, 8204, 8205, 8212, 8265, 8297, 8316, 8336)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 23, "namespace": null, "name": "platform/plugins/language-advanced/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\language-advanced\\src\\Providers\\HookServiceProvider.php", "line": 55}, {"index": 27, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\helpers\\action-filter.php", "line": 39}, {"index": 34, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 36, "namespace": null, "name": "platform/core/support/src/Repositories/Eloquent/RepositoriesAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 366}], "start": **********.05872, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 5.526, "width_percent": 0.417}, {"sql": "select `id`, `is_default`, `exchange_rate`, `symbol`, `title`, `is_prefix_symbol` from `re_currencies` where `re_currencies`.`id` in (4, 10, 25, 36)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 23, "namespace": null, "name": "platform/core/support/src/Repositories/Eloquent/RepositoriesAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 366}, {"index": 24, "namespace": null, "name": "platform/plugins/real-estate/src/Repositories/Eloquent/PropertyRepository.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Repositories\\Eloquent\\PropertyRepository.php", "line": 479}, {"index": 25, "namespace": null, "name": "platform/themes/xmetr/src/Http/Controllers/XmetrController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\themes\\xmetr\\src\\Http\\Controllers\\XmetrController.php", "line": 84}], "start": **********.069329, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 5.943, "width_percent": 0.393}, {"sql": "select `re_categories`.`id`, `re_categories`.`name`, `re_property_categories`.`property_id` as `pivot_property_id`, `re_property_categories`.`category_id` as `pivot_category_id` from `re_categories` inner join `re_property_categories` on `re_categories`.`id` = `re_property_categories`.`category_id` where `re_property_categories`.`property_id` in (1132, 1133, 1135, 1136, 1137, 1138, 1139, 1140, 1141, 1142, 1143, 1144, 1145, 1146, 1147, 1148, 1149, 1150, 1151, 1152) and `status` = 'published' order by `created_at` desc, `is_default` desc, `order` desc", "type": "query", "params": [], "bindings": ["published"], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/support/src/Repositories/Eloquent/RepositoriesAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 366}, {"index": 22, "namespace": null, "name": "platform/plugins/real-estate/src/Repositories/Eloquent/PropertyRepository.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Repositories\\Eloquent\\PropertyRepository.php", "line": 479}, {"index": 23, "namespace": null, "name": "platform/themes/xmetr/src/Http/Controllers/XmetrController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\themes\\xmetr\\src\\Http\\Controllers\\XmetrController.php", "line": 84}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.083222, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 19, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 6.336, "width_percent": 0.409}, {"sql": "select * from `re_properties_translations` where `re_properties_translations`.`re_properties_id` in (1132, 1133, 1135, 1136, 1137, 1138, 1139, 1140, 1141, 1142, 1143, 1144, 1145, 1146, 1147, 1148, 1149, 1150, 1151, 1152) and `re_properties_translations`.`lang_code` = 'ru_RU'", "type": "query", "params": [], "bindings": ["ru_RU"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 23, "namespace": null, "name": "platform/core/support/src/Repositories/Eloquent/RepositoriesAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 366}, {"index": 24, "namespace": null, "name": "platform/plugins/real-estate/src/Repositories/Eloquent/PropertyRepository.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Repositories\\Eloquent\\PropertyRepository.php", "line": 479}, {"index": 25, "namespace": null, "name": "platform/themes/xmetr/src/Http/Controllers/XmetrController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\themes\\xmetr\\src\\Http\\Controllers\\XmetrController.php", "line": 84}], "start": **********.0978692, "duration": 0.00151, "duration_str": "1.51ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 6.745, "width_percent": 1.187}, {"sql": "select * from `re_currencies` order by `order` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "platform/plugins/real-estate/src/Supports/CurrencySupport.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Supports\\CurrencySupport.php", "line": 107}, {"index": 17, "namespace": null, "name": "platform/plugins/real-estate/src/Supports/CurrencySupport.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Supports\\CurrencySupport.php", "line": 41}, {"index": 18, "namespace": null, "name": "platform/plugins/real-estate/helpers/currencies.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\helpers\\currencies.php", "line": 142}, {"index": 28, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}], "start": **********.276384, "duration": 0.00082, "duration_str": "820μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 7.932, "width_percent": 0.645}, {"sql": "select * from `re_categories_translations` where `re_categories_translations`.`re_categories_id` = 7 and `re_categories_translations`.`re_categories_id` is not null", "type": "query", "params": [], "bindings": [7], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/plugins/language-advanced/src/Supports/LanguageAdvancedManager.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\language-advanced\\src\\Supports\\LanguageAdvancedManager.php", "line": 227}, {"index": 28, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 25}, {"index": 29, "namespace": null, "name": "platform/plugins/real-estate/src/Models/Property.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Models\\Property.php", "line": 199}], "start": **********.3047822, "duration": 0.0112, "duration_str": "11.2ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 8.576, "width_percent": 8.804}, {"sql": "select * from `re_categories_translations` where `re_categories_translations`.`re_categories_id` = 7 and `re_categories_translations`.`re_categories_id` is not null", "type": "query", "params": [], "bindings": [7], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/plugins/language-advanced/src/Supports/LanguageAdvancedManager.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\language-advanced\\src\\Supports\\LanguageAdvancedManager.php", "line": 227}, {"index": 28, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 25}, {"index": 29, "namespace": null, "name": "platform/plugins/real-estate/src/Models/Property.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Models\\Property.php", "line": 199}], "start": **********.3605042, "duration": 0.06666, "duration_str": "66.66ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 17.381, "width_percent": 52.402}, {"sql": "select * from `re_categories_translations` where `re_categories_translations`.`re_categories_id` = 7 and `re_categories_translations`.`re_categories_id` is not null", "type": "query", "params": [], "bindings": [7], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/plugins/language-advanced/src/Supports/LanguageAdvancedManager.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\language-advanced\\src\\Supports\\LanguageAdvancedManager.php", "line": 227}, {"index": 28, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 25}, {"index": 29, "namespace": null, "name": "platform/plugins/real-estate/src/Models/Property.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Models\\Property.php", "line": 199}], "start": **********.460757, "duration": 0.01455, "duration_str": "14.55ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 69.782, "width_percent": 11.438}, {"sql": "select * from `re_categories_translations` where `re_categories_translations`.`re_categories_id` = 7 and `re_categories_translations`.`re_categories_id` is not null", "type": "query", "params": [], "bindings": [7], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/plugins/language-advanced/src/Supports/LanguageAdvancedManager.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\language-advanced\\src\\Supports\\LanguageAdvancedManager.php", "line": 227}, {"index": 28, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 25}, {"index": 29, "namespace": null, "name": "platform/plugins/real-estate/src/Models/Property.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Models\\Property.php", "line": 199}], "start": **********.509359, "duration": 0.015, "duration_str": "15ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 81.22, "width_percent": 11.792}, {"sql": "select * from `re_categories_translations` where `re_categories_translations`.`re_categories_id` = 7 and `re_categories_translations`.`re_categories_id` is not null", "type": "query", "params": [], "bindings": [7], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/plugins/language-advanced/src/Supports/LanguageAdvancedManager.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\language-advanced\\src\\Supports\\LanguageAdvancedManager.php", "line": 227}, {"index": 28, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 25}, {"index": 29, "namespace": null, "name": "platform/plugins/real-estate/src/Models/Property.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Models\\Property.php", "line": 199}], "start": **********.561476, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 93.012, "width_percent": 0.503}, {"sql": "select * from `re_categories_translations` where `re_categories_translations`.`re_categories_id` = 7 and `re_categories_translations`.`re_categories_id` is not null", "type": "query", "params": [], "bindings": [7], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/plugins/language-advanced/src/Supports/LanguageAdvancedManager.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\language-advanced\\src\\Supports\\LanguageAdvancedManager.php", "line": 227}, {"index": 28, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 25}, {"index": 29, "namespace": null, "name": "platform/plugins/real-estate/src/Models/Property.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Models\\Property.php", "line": 199}], "start": **********.5947008, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 93.515, "width_percent": 0.597}, {"sql": "select * from `re_categories_translations` where `re_categories_translations`.`re_categories_id` = 4 and `re_categories_translations`.`re_categories_id` is not null", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/plugins/language-advanced/src/Supports/LanguageAdvancedManager.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\language-advanced\\src\\Supports\\LanguageAdvancedManager.php", "line": 227}, {"index": 28, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 25}, {"index": 29, "namespace": null, "name": "platform/plugins/real-estate/src/Models/Property.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Models\\Property.php", "line": 199}], "start": **********.629694, "duration": 0.00166, "duration_str": "1.66ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 94.112, "width_percent": 1.305}, {"sql": "select * from `re_categories_translations` where `re_categories_translations`.`re_categories_id` = 8 and `re_categories_translations`.`re_categories_id` is not null", "type": "query", "params": [], "bindings": [8], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/plugins/language-advanced/src/Supports/LanguageAdvancedManager.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\language-advanced\\src\\Supports\\LanguageAdvancedManager.php", "line": 227}, {"index": 28, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 25}, {"index": 29, "namespace": null, "name": "platform/plugins/real-estate/src/Models/Property.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Models\\Property.php", "line": 199}], "start": **********.6548681, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 95.417, "width_percent": 0.346}, {"sql": "select * from `re_categories_translations` where `re_categories_translations`.`re_categories_id` = 7 and `re_categories_translations`.`re_categories_id` is not null", "type": "query", "params": [], "bindings": [7], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/plugins/language-advanced/src/Supports/LanguageAdvancedManager.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\language-advanced\\src\\Supports\\LanguageAdvancedManager.php", "line": 227}, {"index": 28, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 25}, {"index": 29, "namespace": null, "name": "platform/plugins/real-estate/src/Models/Property.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Models\\Property.php", "line": 199}], "start": **********.675896, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 95.763, "width_percent": 0.338}, {"sql": "select * from `re_categories_translations` where `re_categories_translations`.`re_categories_id` = 7 and `re_categories_translations`.`re_categories_id` is not null", "type": "query", "params": [], "bindings": [7], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/plugins/language-advanced/src/Supports/LanguageAdvancedManager.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\language-advanced\\src\\Supports\\LanguageAdvancedManager.php", "line": 227}, {"index": 28, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 25}, {"index": 29, "namespace": null, "name": "platform/plugins/real-estate/src/Models/Property.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Models\\Property.php", "line": 199}], "start": **********.690758, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 96.101, "width_percent": 0.338}, {"sql": "select * from `re_categories_translations` where `re_categories_translations`.`re_categories_id` = 7 and `re_categories_translations`.`re_categories_id` is not null", "type": "query", "params": [], "bindings": [7], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/plugins/language-advanced/src/Supports/LanguageAdvancedManager.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\language-advanced\\src\\Supports\\LanguageAdvancedManager.php", "line": 227}, {"index": 28, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 25}, {"index": 29, "namespace": null, "name": "platform/plugins/real-estate/src/Models/Property.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Models\\Property.php", "line": 199}], "start": **********.716078, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 96.439, "width_percent": 0.377}, {"sql": "select * from `re_categories_translations` where `re_categories_translations`.`re_categories_id` = 7 and `re_categories_translations`.`re_categories_id` is not null", "type": "query", "params": [], "bindings": [7], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/plugins/language-advanced/src/Supports/LanguageAdvancedManager.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\language-advanced\\src\\Supports\\LanguageAdvancedManager.php", "line": 227}, {"index": 28, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 25}, {"index": 29, "namespace": null, "name": "platform/plugins/real-estate/src/Models/Property.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Models\\Property.php", "line": 199}], "start": **********.7334468, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 96.816, "width_percent": 0.338}, {"sql": "select * from `re_categories_translations` where `re_categories_translations`.`re_categories_id` = 7 and `re_categories_translations`.`re_categories_id` is not null", "type": "query", "params": [], "bindings": [7], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/plugins/language-advanced/src/Supports/LanguageAdvancedManager.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\language-advanced\\src\\Supports\\LanguageAdvancedManager.php", "line": 227}, {"index": 28, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 25}, {"index": 29, "namespace": null, "name": "platform/plugins/real-estate/src/Models/Property.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Models\\Property.php", "line": 199}], "start": **********.750998, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 97.154, "width_percent": 0.369}, {"sql": "select * from `re_categories_translations` where `re_categories_translations`.`re_categories_id` = 7 and `re_categories_translations`.`re_categories_id` is not null", "type": "query", "params": [], "bindings": [7], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/plugins/language-advanced/src/Supports/LanguageAdvancedManager.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\language-advanced\\src\\Supports\\LanguageAdvancedManager.php", "line": 227}, {"index": 28, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 25}, {"index": 29, "namespace": null, "name": "platform/plugins/real-estate/src/Models/Property.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Models\\Property.php", "line": 199}], "start": **********.766989, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 97.524, "width_percent": 0.377}, {"sql": "select * from `re_categories_translations` where `re_categories_translations`.`re_categories_id` = 7 and `re_categories_translations`.`re_categories_id` is not null", "type": "query", "params": [], "bindings": [7], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/plugins/language-advanced/src/Supports/LanguageAdvancedManager.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\language-advanced\\src\\Supports\\LanguageAdvancedManager.php", "line": 227}, {"index": 28, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 25}, {"index": 29, "namespace": null, "name": "platform/plugins/real-estate/src/Models/Property.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Models\\Property.php", "line": 199}], "start": **********.7815979, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 97.901, "width_percent": 0.322}, {"sql": "select * from `re_categories_translations` where `re_categories_translations`.`re_categories_id` = 7 and `re_categories_translations`.`re_categories_id` is not null", "type": "query", "params": [], "bindings": [7], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/plugins/language-advanced/src/Supports/LanguageAdvancedManager.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\language-advanced\\src\\Supports\\LanguageAdvancedManager.php", "line": 227}, {"index": 28, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 25}, {"index": 29, "namespace": null, "name": "platform/plugins/real-estate/src/Models/Property.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Models\\Property.php", "line": 199}], "start": **********.812927, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 98.223, "width_percent": 0.377}, {"sql": "select * from `re_categories_translations` where `re_categories_translations`.`re_categories_id` = 7 and `re_categories_translations`.`re_categories_id` is not null", "type": "query", "params": [], "bindings": [7], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/plugins/language-advanced/src/Supports/LanguageAdvancedManager.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\language-advanced\\src\\Supports\\LanguageAdvancedManager.php", "line": 227}, {"index": 28, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 25}, {"index": 29, "namespace": null, "name": "platform/plugins/real-estate/src/Models/Property.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Models\\Property.php", "line": 199}], "start": **********.8440602, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 98.601, "width_percent": 0.354}, {"sql": "select * from `re_categories_translations` where `re_categories_translations`.`re_categories_id` = 7 and `re_categories_translations`.`re_categories_id` is not null", "type": "query", "params": [], "bindings": [7], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/plugins/language-advanced/src/Supports/LanguageAdvancedManager.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\language-advanced\\src\\Supports\\LanguageAdvancedManager.php", "line": 227}, {"index": 28, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 25}, {"index": 29, "namespace": null, "name": "platform/plugins/real-estate/src/Models/Property.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Models\\Property.php", "line": 199}], "start": **********.871516, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 98.954, "width_percent": 0.283}, {"sql": "select * from `re_categories_translations` where `re_categories_translations`.`re_categories_id` = 7 and `re_categories_translations`.`re_categories_id` is not null", "type": "query", "params": [], "bindings": [7], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/plugins/language-advanced/src/Supports/LanguageAdvancedManager.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\language-advanced\\src\\Supports\\LanguageAdvancedManager.php", "line": 227}, {"index": 28, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 25}, {"index": 29, "namespace": null, "name": "platform/plugins/real-estate/src/Models/Property.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Models\\Property.php", "line": 199}], "start": **********.903346, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 99.237, "width_percent": 0.362}, {"sql": "select * from `re_categories_translations` where `re_categories_translations`.`re_categories_id` = 7 and `re_categories_translations`.`re_categories_id` is not null", "type": "query", "params": [], "bindings": [7], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/plugins/language-advanced/src/Supports/LanguageAdvancedManager.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\language-advanced\\src\\Supports\\LanguageAdvancedManager.php", "line": 227}, {"index": 28, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 25}, {"index": 29, "namespace": null, "name": "platform/plugins/real-estate/src/Models/Property.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Models\\Property.php", "line": 199}], "start": **********.931255, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 99.599, "width_percent": 0.401}]}, "models": {"data": {"Xmetr\\LanguageAdvanced\\Models\\TranslationResolver": {"value": 202, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Flanguage-advanced%2Fsrc%2FModels%2FTranslationResolver.php:1", "ajax": false, "filename": "TranslationResolver.php", "line": "?"}}, "Xmetr\\RealEstate\\Models\\Currency": {"value": 47, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Freal-estate%2Fsrc%2FModels%2FCurrency.php:1", "ajax": false, "filename": "Currency.php", "line": "?"}}, "Xmetr\\Slug\\Models\\Slug": {"value": 40, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fpackages%2Fslug%2Fsrc%2FModels%2FSlug.php:1", "ajax": false, "filename": "Slug.php", "line": "?"}}, "Xmetr\\RealEstate\\Models\\Category": {"value": 22, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Freal-estate%2Fsrc%2FModels%2FCategory.php:1", "ajax": false, "filename": "Category.php", "line": "?"}}, "Xmetr\\RealEstate\\Models\\Property": {"value": 20, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Freal-estate%2Fsrc%2FModels%2FProperty.php:1", "ajax": false, "filename": "Property.php", "line": "?"}}, "Xmetr\\Location\\Models\\City": {"value": 8, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Flocation%2Fsrc%2FModels%2FCity.php:1", "ajax": false, "filename": "City.php", "line": "?"}}, "Xmetr\\Location\\Models\\State": {"value": 1, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Flocation%2Fsrc%2FModels%2FState.php:1", "ajax": false, "filename": "State.php", "line": "?"}}}, "count": 340, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "https://xmetr.gc/ru/ajax/properties/map?page=2", "action_name": "public.ajax.properties.map", "controller_action": "Theme\\Xmetr\\Http\\Controllers\\XmetrController@ajaxGetPropertiesForMap", "uri": "GET ru/ajax/properties/map", "controller": "Theme\\Xmetr\\Http\\Controllers\\XmetrController@ajaxGetPropertiesForMap<a href=\"vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fthemes%2Fxmetr%2Fsrc%2FHttp%2FControllers%2FXmetrController.php:57\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "ru/ajax", "file": "<a href=\"vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fthemes%2Fxmetr%2Fsrc%2FHttp%2FControllers%2FXmetrController.php:57\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">platform/themes/xmetr/src/Http/Controllers/XmetrController.php:57-90</a>", "middleware": "web, core, localeSessionRedirect, localizationRedirect, Xmetr\\Base\\Http\\Middleware\\RequiresJsonRequestMiddleware", "duration": "2.11s", "peak_memory": "64MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-911769378 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>page</span>\" => \"<span class=sf-dump-str>2</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-911769378\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1123027321 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1123027321\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-515226296 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">xmetr.gc</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"44 characters\">https://xmetr.gc/ru/mietobjekte/france-paris</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"50 characters\">en-US,en;q=0.9,ur;q=0.8,pl;q=0.7,ru;q=0.6,ar;q=0.5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1687 characters\">remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IjE5eVFoNXI3S3dCZTVpWVUvb1ZlTkE9PSIsInZhbHVlIjoiVFFEOHg1MHRNUCtIQndDL1FUdThDSVJxSzFQTTlRRWNjUGJtalZRTmpvRFQrQ1UybGVrWnR6dUN0ZU16VVJXRWg0UVg5cFdaQmlmL212RUdPSjJLcUgray8wa3JyeFU3LzUrRUs3by9jVXM1cCt0MDZCTXZjczI2RTRNR0xqeTBJT1dpcWNNQTdEeW8wYkZ1S3lUc1pZSVFCd2FiN2EvdTRmZGdiU2FOYVREVUtPdGw5SG1xOUZHVU5Db0Zwa1lvVTRXRDFPOXBhNnZOQ3hkWjJjU2JtaDRQRU9WYXRwczhPTmM1Wit2aThBcz0iLCJtYWMiOiI0NThmZWI3NDI2NThmMTIxOWRlN2JiMjljY2Y3MjQzMDUzNmQyYjA5NzllZTg4YmY2ZTc5MGExYjEzODJhMGU1IiwidGFnIjoiIn0%3D; _ga=GA1.1.2064844405.1743885089; cookie_for_consent=1; wishlist=657; _hjSessionUser_6417422=eyJpZCI6IjhkMzA2ODc1LTcwNjctNTU3Yi1hNDljLTdhNmEyNTdkM2QxOCIsImNyZWF0ZWQiOjE3NDgzODAwMDM5NTcsImV4aXN0aW5nIjp0cnVlfQ==; _hjSession_6417422=eyJpZCI6ImVlNDZjNjhjLTVjNjEtNGVlOS1hMjI0LTRmMDZjNmU3ZGY4NCIsImMiOjE3NTEwMzQ2NzE1ODcsInMiOjEsInIiOjAsInNiIjowLCJzciI6MCwic2UiOjAsImZzIjowfQ==; _ga_KQ6X76DET4=GS2.1.s1751034674$o76$g1$t1751036855$j42$l0$h0; XSRF-TOKEN=eyJpdiI6InloWlFXSFhsb2RmV2ZlcTBRUXdZV3c9PSIsInZhbHVlIjoiT0JQYTlxZWpXK2JKSHc4T29yVElybnBIUnI0OFpmbkpIejE5Qzk1MjZhZGFHbE5HcUo1RkVSRTZ5UkxrTTJPZkZmS29HYUNZUHRPa0lRMjNvR1phbERnSVdMWUxTNm9FNDRtUUhsZG45ZVg4dnI0MVducWwvSnJFNHFhYjdubnYiLCJtYWMiOiI3MDQ1MDIxNDI0YmY1Y2JmZjAyNTAyMzk1ODE0NWE0ZTc3NGRhZGYzMjAzODMyZWJiZWJkN2IzNGEzZDYzMDZkIiwidGFnIjoiIn0%3D; xmetr_session=eyJpdiI6Ik1BMlYvVmdBcHg2Um5ncUVYNC8vNlE9PSIsInZhbHVlIjoibmFBeEZZVEJaNTVQS1YxclFxTXlyZnRNeFRsZHdvdTFYQmI0K29kUUdFdW1va044czFMM3IxK3hyKzFRQ1prZXpyUTdnNXUvSUhCWXU1Z0Z3dEoyZnJGVkF5NGpXb0k4OVBhT2h4R1M5VUVrVThBQ0c2M05VWDQ5UnNyWjBINDAiLCJtYWMiOiJmZjY2OTc1ZDJhOTAzNzE2YjFhZTE2NmZmYjA4M2Y2ZWIzMDYyYmRjNjY1MzhjZDc2YzdiZjRiY2Q2ZjRjODI0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-515226296\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1609174582 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">1|1uQwj4bI0uiYsJi0APplQif6Zji5Zq40vBOmafc1oj6NYK3vu89uqM3GLC7O|$2y$04$Astns3SXblBceX03zRicBuVKoyIRRFaOY1GuvREgnRYhHEelfNbdu</span>\"\n  \"<span class=sf-dump-key>_ga</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_for_consent</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>wishlist</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_hjSessionUser_6417422</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_hjSession_6417422</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_ga_KQ6X76DET4</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Gi5RJZ0ijqaVr6Lg3jFaXX5hF7p742ZKiCmqC77D</span>\"\n  \"<span class=sf-dump-key>xmetr_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">crfBFjn65dZVGczOyRKD4yPI4R8TEIsbnbtVi55O</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1609174582\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1158303826 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 15:07:40 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1158303826\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1887883127 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Gi5RJZ0ijqaVr6Lg3jFaXX5hF7p742ZKiCmqC77D</span>\"\n  \"<span class=sf-dump-key>language</span>\" => \"<span class=sf-dump-str title=\"2 characters\">ru</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>new</span>\" => []\n    \"<span class=sf-dump-key>old</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"52 characters\">https://xmetr.gc/ru/arenda-nedvizimosti/france-paris</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n  \"<span class=sf-dump-key>previous_language</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1887883127\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "https://xmetr.gc/ru/ajax/properties/map?page=2", "action_name": "public.ajax.properties.map", "controller_action": "Theme\\Xmetr\\Http\\Controllers\\XmetrController@ajaxGetPropertiesForMap"}, "badge": null}}