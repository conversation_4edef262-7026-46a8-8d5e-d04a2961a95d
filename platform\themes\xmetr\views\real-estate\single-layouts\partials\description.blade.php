

<div @class(['grid grid-cols-3 gap-y-[20px] gap-x-[16px] max-[1024px]:grid-cols-2', $class ?? null])>

    {{-- <div class="col item">
        <div class="box-icon w-52">
            <x-core::icon name="ti ti-home" />
        </div>
        <div class="content">
            <span class="label">{{ __('Property ID:') }}</span>
            <span>{{ $property->unique_id ?: $property->getKey() }}</span>
        </div>
    </div> --}}
    @if($property->categories->isNotEmpty())
        <div class="flex items-center gap-[10px]">
            <div class="w-[50px] h-[50px] bg-white rounded-[10px] border border-[#D6D6D7] flex justify-center items-center">
            <p class="text-[25px]">🏘</p>
            </div>

            <div class="flex flex-col">
            <p class="text-black text-[15px] font-bold">{{ __('Type') }}</p>
            <p class="text-black text-[15px]">{{ implode(', ', $property->categories->map(function($category) { return $category->name; })->toArray()) }}</p>
            </div>
        </div>
    @endif


    {{-- <div class="flex items-center gap-[10px]">
        <div class="w-[50px] h-[50px] bg-white rounded-[10px] border border-[#D6D6D7] flex justify-center items-center">
        <p class="text-[25px]">🏗️</p>
        </div>

        <div class="flex flex-col">
        <p class="text-black text-[15px] font-bold">{{ __('Built') }}</p>
        <p class="text-black text-[15px]">2022</p>
        </div>
    </div> --}}

    @if ($property->square)
    <div class="flex items-center gap-[10px]">
        <div class="w-[50px] h-[50px] bg-white rounded-[10px] border border-[#D6D6D7] flex justify-center items-center">
          <p class="text-[25px]">📐</p>
        </div>

        <div class="flex flex-col">
          <p class="text-black text-[15px] font-bold">{{ __('Area м²') }}</p>
          <p class="text-black text-[15px]">{{ $property->square_text }}</p>
        </div>
      </div>
    @endif

    @if ($property->number_floor)
    <div class="flex items-center gap-[10px]">
        <div class="w-[50px] h-[50px] bg-white rounded-[10px] border border-[#D6D6D7] flex justify-center items-center">
          <p class="text-[25px]">🚪</p>
        </div>

        <div class="flex flex-col">
          <p class="text-black text-[15px] font-bold">{{ __('Floors') }}</p>
          <p class="text-black text-[15px]">{{ number_format($property->number_floor) }}</p>
        </div>
      </div>

    @endif

    @if ($property->number_bedroom)
        <div class="flex items-center gap-[10px]">
            <div class="w-[50px] h-[50px] bg-white rounded-[10px] border border-[#D6D6D7] flex justify-center items-center">
            <p class="text-[25px]">🛌</p>
            </div>

            <div class="flex flex-col">
            <p class="text-black text-[15px] font-bold">{{ __('Bedrooms') }}</p>
            <p class="text-black text-[15px]">{{ number_format($property->number_bedroom) }}</p>
            </div>
        </div>
    @endif
    @if ($property->number_bathroom)
        <div class="flex items-center gap-[10px]">
            <div class="w-[50px] h-[50px] bg-white rounded-[10px] border border-[#D6D6D7] flex justify-center items-center">
            <p class="text-[25px]">🛀</p>
            </div>

            <div class="flex flex-col">
            <p class="text-black text-[15px] font-bold">{{ __('Bathrooms') }}</p>
            <p class="text-black text-[15px]">{{ number_format($property->number_bathroom) }}</p>
            </div>
        </div>
    @endif
    @if ($property->district_id)
        <div class="flex items-center gap-[10px]">
            <div class="w-[50px] h-[50px] bg-white rounded-[10px] border border-[#D6D6D7] flex justify-center items-center">
            <p class="text-[25px]">📶</p>
            </div>

            <div class="flex flex-col">
            <p class="text-black text-[15px] font-bold">{{ __('District Ratings') }}</p>
            <p class="text-black text-[15px]">{{ number_format($property->district->average_rating,1) }} / 5</p>
            </div>
        </div>
    @endif



</div>
<span class="w-full h-[1px] block bg-[#D6D6D7]"></span>

@if ($property->content || $property->private_notes || $property->original_description)

    @if($property->content)
        <h4 class="title">{{ __('Description') }}</h4>
          @if($property->original_description)
          <p>{{ __('This text translated') }} <button id="toggleButton" class="text-[#5E2DC2] font-bold underline">{{ __('Show Original') }}</button></p>
           <div id="originalDescription" style="display: none;">
                {!! BaseHelper::clean($property->original_description) !!}
           </div>

    <style>
    #originalDescription {
        border: 1px solid #e9e9e9;
        padding: 10px;
        background-color: #f9f9f9;
        border-radius: 10px;
    }
    #originalDescription p {
        margin-bottom: 0;
    }
  </style>
    <script>
    const toggleBtn = document.getElementById("toggleButton");
    const descriptionBox = document.getElementById("originalDescription");

    toggleBtn.addEventListener("click", function () {
      if (descriptionBox.style.display === "none") {
        descriptionBox.style.display = "block";
        toggleBtn.textContent = "{{ __('Close') }}";
      } else {
        descriptionBox.style.display = "none";
        toggleBtn.textContent = "{{ __('Show Original') }}";
      }
    });
  </script>
            @endif
        <div class="text-black text-[15px]">
            {!! BaseHelper::clean($property->content) !!}
        </div>
    @endif

    @if($property->can_see_private_notes && $property->private_notes)
        <h4 class="title">{{ __('Private Notes') }}</h4>
        <div class="text-black text-[15px]">
        {!! BaseHelper::clean(nl2br($property->private_notes)) !!}
        </div>
    @endif

<span class="w-full h-[1px] block bg-[#D6D6D7]"></span>
@endif


