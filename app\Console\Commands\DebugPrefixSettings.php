<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use <PERSON>metr\Slug\Models\Slug;
use Xmetr\Language\Facades\Language;
use Xmetr\Slug\Facades\SlugHelper;

class DebugPrefixSettings extends Command
{
    protected $signature = 'debug:prefix-settings';
    protected $description = 'Debug prefix settings for different languages';

    public function handle()
    {
        $this->info('=== DEBUGGING PREFIX SETTINGS ===');
        $this->newLine();

        // Test the france-paris slug specifically
        $slug = Slug::find(2107); // france-paris
        if (!$slug) {
            $this->error('Slug 2107 not found');
            return;
        }

        $this->info("Testing slug: {$slug->key} (ID: {$slug->id})");
        $this->info("Model: {$slug->reference_type}");
        $this->info("Main prefix: '{$slug->prefix}'");
        $this->newLine();

        // Test different languages
        $languages = ['ru_RU', 'de_DE', 'es_CL', 'fr_FR'];
        
        foreach ($languages as $langCode) {
            $this->info("Language: {$langCode}");
            
            // Get the permalink setting key
            $settingKey = SlugHelper::getPermalinkSettingKey($slug->reference_type);
            $this->line("  Base setting key: {$settingKey}");
            
            // Get language-specific key
            $langSettingKey = $settingKey . '-' . $langCode;
            $this->line("  Language setting key: {$langSettingKey}");
            
            // Get the actual setting values
            $baseSetting = setting($settingKey);
            $langSetting = setting($langSettingKey);
            
            $this->line("  Base setting value: '{$baseSetting}'");
            $this->line("  Language setting value: '{$langSetting}'");
            
            // Test the function
            $correctPrefix = $this->getCorrectPrefixForLanguage($slug->reference_type, $langCode);
            $this->line("  Calculated correct prefix: '{$correctPrefix}'");
            
            // Get current translation
            $currentTranslation = DB::table('slugs_translations')
                ->where('slugs_id', $slug->id)
                ->where('lang_code', $langCode)
                ->first();
                
            if ($currentTranslation) {
                $this->line("  Current translation prefix: '{$currentTranslation->prefix}'");
                $needsUpdate = $currentTranslation->prefix !== $correctPrefix;
                $this->line("  Needs update: " . ($needsUpdate ? 'YES' : 'NO'));
            } else {
                $this->line("  No translation found");
            }
            
            $this->newLine();
        }
    }

    private function getCorrectPrefixForLanguage(string $modelClass, string $langCode): string
    {
        // Get the permalink setting key for this model
        $settingKey = SlugHelper::getPermalinkSettingKey($modelClass);
        
        // Get the language-specific setting key
        $langSettingKey = $settingKey . '-' . $langCode;
        
        // Get the setting value for this specific language
        $prefix = setting($langSettingKey);
        
        // If no language-specific setting exists, fall back to default
        if (!$prefix) {
            $prefix = setting($settingKey);
        }
        
        // If still no prefix, get from SlugHelper
        if (!$prefix) {
            $prefix = SlugHelper::getPrefix($modelClass, '', false);
        }
        
        return $prefix ?: '';
    }
}
