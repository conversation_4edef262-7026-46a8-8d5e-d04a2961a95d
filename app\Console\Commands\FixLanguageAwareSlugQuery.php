<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\File;

class FixLanguageAwareSlugQuery extends Command
{
    protected $signature = 'fix:language-aware-slug-query {--dry-run : Show what would be changed without making changes}';
    protected $description = 'Fix the slug query to be language-aware';

    public function handle()
    {
        $isDryRun = $this->option('dry-run');
        
        if ($isDryRun) {
            $this->info('=== DRY RUN MODE - No changes will be made ===');
        } else {
            $this->info('=== FIXING LANGUAGE-AWARE SLUG QUERY ===');
        }
        
        $this->newLine();

        $filePath = 'platform/plugins/language-advanced/src/Providers/HookServiceProvider.php';
        
        if (!File::exists($filePath)) {
            $this->error("File not found: {$filePath}");
            return 1;
        }

        $content = File::get($filePath);
        
        // Find the current getSlugQuery method
        $oldMethod = '    public function getSlugQuery(EloquentBuilder $query, array $condition = []): EloquentBuilder
    {
        try {
            return $query
                ->orWhereHas(\'translations\', function (EloquentBuilder $query) use ($condition) {
                    return $query->where($condition);
                });
        } catch (Throwable) {
            return $query;
        }
    }';

        // New language-aware method
        $newMethod = '    public function getSlugQuery(EloquentBuilder $query, array $condition = []): EloquentBuilder
    {
        try {
            return $query
                ->orWhereHas(\'translations\', function (EloquentBuilder $query) use ($condition) {
                    $query->where($condition);
                    
                    // Make slug resolution language-aware
                    $currentLocale = Language::getCurrentLocale();
                    if ($currentLocale && $currentLocale !== Language::getDefaultLocale()) {
                        // Convert short locale to full locale format (e.g., ru -> ru_RU)
                        $supportedLocales = Language::getSupportedLocales();
                        $fullLocale = null;
                        
                        foreach ($supportedLocales as $localeCode => $localeData) {
                            if (substr($localeCode, 0, 2) === $currentLocale) {
                                $fullLocale = $localeCode;
                                break;
                            }
                        }
                        
                        if ($fullLocale) {
                            $query->where(\'lang_code\', $fullLocale);
                        }
                    }
                    
                    return $query;
                });
        } catch (Throwable) {
            return $query;
        }
    }';

        if (strpos($content, $oldMethod) === false) {
            $this->error('Could not find the target method to replace.');
            $this->line('Looking for:');
            $this->line($oldMethod);
            return 1;
        }

        $newContent = str_replace($oldMethod, $newMethod, $content);
        
        if ($isDryRun) {
            $this->info('CHANGES TO BE MADE:');
            $this->newLine();
            $this->line('OLD METHOD:');
            $this->line($oldMethod);
            $this->newLine();
            $this->line('NEW METHOD:');
            $this->line($newMethod);
            $this->newLine();
            $this->info('Run without --dry-run to apply the changes');
        } else {
            File::put($filePath, $newContent);
            $this->info('✅ Successfully updated the getSlugQuery method to be language-aware!');
            $this->newLine();
            $this->info('WHAT THIS FIX DOES:');
            $this->line('- When resolving slugs, it now filters by the current language context');
            $this->line('- URLs like /ru/mietobjekte/france-paris will now return 404 (correct behavior)');
            $this->line('- URLs like /ru/arenda-nedvizimosti/france-paris will work (correct behavior)');
            $this->line('- Each language will only find slugs for its own translations');
            $this->newLine();
            $this->warn('⚠️  IMPORTANT: Clear your application cache after this change:');
            $this->line('php artisan cache:clear');
            $this->line('php artisan config:clear');
            $this->line('php artisan route:clear');
        }

        return 0;
    }
}
