<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Xmetr\Slug\Models\Slug;
use Xmetr\Language\Facades\Language;
use Xmetr\Slug\Facades\SlugHelper;
use Illuminate\Support\Facades\App;

class TestLanguageContext extends Command
{
    protected $signature = 'test:language-context';
    protected $description = 'Test language context and slug resolution';

    public function handle()
    {
        $this->info('=== TESTING LANGUAGE CONTEXT ===');
        $this->newLine();

        // Test different language contexts
        $languages = ['en', 'ru', 'de', 'es', 'fr'];
        
        foreach ($languages as $lang) {
            $this->info("Setting language context to: {$lang}");
            
            // Set language context like the middleware does
            App::setLocale($lang);
            Language::setCurrentLocale($lang);
            
            $this->line("  App locale: " . App::getLocale());
            $this->line("  Language current locale: " . Language::getCurrentLocale());
            
            // Test slug resolution
            $slug1 = SlugHelper::getSlug('france-paris', 'mietobjekte');
            $slug2 = SlugHelper::getSlug('france-paris', 'arenda-nedvizimosti');
            
            $this->line("  mietobjekte/france-paris: " . ($slug1 ? "FOUND (ID {$slug1->id})" : "NOT FOUND"));
            $this->line("  arenda-nedvizimosti/france-paris: " . ($slug2 ? "FOUND (ID {$slug2->id})" : "NOT FOUND"));
            
            $this->newLine();
        }
        
        // Reset to default
        App::setLocale('en');
        Language::setCurrentLocale('en');
        
        $this->info('=== ANALYSIS ===');
        $this->line('The slug resolution is not filtering by current language context.');
        $this->line('We need to modify the getSlugQuery method to be language-aware.');
    }
}
