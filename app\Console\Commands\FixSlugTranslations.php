<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use <PERSON><PERSON>r\Slug\Models\Slug;
use Xmetr\Language\Facades\Language;
use Xmetr\Slug\Facades\SlugHelper;

class FixSlugTranslations extends Command
{
    protected $signature = 'fix:slug-translations {--dry-run : Show what would be changed without making changes}';
    protected $description = 'Fix slug translations to use correct language-specific prefixes';

    public function handle()
    {
        $isDryRun = $this->option('dry-run');
        
        if ($isDryRun) {
            $this->info('=== DRY RUN MODE - No changes will be made ===');
        } else {
            $this->info('=== FIXING SLUG TRANSLATIONS ===');
        }
        
        $this->newLine();

        // Get all active languages except default
        $languages = DB::table('languages')
            ->where('lang_is_default', 0)
            ->get(['lang_code', 'lang_name']);

        $this->info("Found " . $languages->count() . " non-default languages to process");

        $totalFixed = 0;

        foreach ($languages as $language) {
            $this->info("Processing language: {$language->lang_code} ({$language->lang_name})");
            
            // Get all slug translations for this language
            $translations = DB::table('slugs_translations')
                ->where('lang_code', $language->lang_code)
                ->get();

            $this->line("  Found {$translations->count()} translations for {$language->lang_code}");

            $fixedCount = 0;

            foreach ($translations as $translation) {
                // Get the main slug to determine the model type
                $mainSlug = Slug::find($translation->slugs_id);
                
                if (!$mainSlug) {
                    $this->warn("  Warning: Main slug {$translation->slugs_id} not found");
                    continue;
                }

                // Get the correct prefix for this model and language
                $correctPrefix = $this->getCorrectPrefixForLanguage($mainSlug->reference_type, $language->lang_code);
                
                if ($correctPrefix !== $translation->prefix) {
                    $this->line("    Slug ID {$translation->slugs_id} ({$translation->key}): '{$translation->prefix}' -> '{$correctPrefix}'");
                    
                    if (!$isDryRun) {
                        DB::table('slugs_translations')
                            ->where('slugs_id', $translation->slugs_id)
                            ->where('lang_code', $language->lang_code)
                            ->update(['prefix' => $correctPrefix]);
                    }
                    
                    $fixedCount++;
                    $totalFixed++;
                }
            }

            if ($fixedCount === 0) {
                $this->line("  No fixes needed for {$language->lang_code}");
            } else {
                $this->line("  Fixed {$fixedCount} translations for {$language->lang_code}");
            }
            
            $this->newLine();
        }

        if ($isDryRun) {
            $this->info("DRY RUN COMPLETE: {$totalFixed} translations would be fixed");
            $this->info("Run without --dry-run to apply the changes");
        } else {
            $this->info("COMPLETE: Fixed {$totalFixed} slug translations");
        }
    }

    private function getCorrectPrefixForLanguage(string $modelClass, string $langCode): string
    {
        // Get the permalink setting key for this model
        $settingKey = SlugHelper::getPermalinkSettingKey($modelClass);
        
        // Convert full language code to short code for settings lookup
        // e.g., ru_RU -> ru, de_DE -> de, es_CL -> es, fr_FR -> fr
        $shortLangCode = substr($langCode, 0, 2);
        
        // Get the language-specific setting key using short code
        $langSettingKey = $settingKey . '-' . $shortLangCode;
        
        // Get the setting value for this specific language
        $prefix = setting($langSettingKey);
        
        // If no language-specific setting exists, fall back to default
        if (!$prefix) {
            $prefix = setting($settingKey);
        }
        
        // If still no prefix, get from SlugHelper
        if (!$prefix) {
            $prefix = SlugHelper::getPrefix($modelClass, '', false);
        }
        
        return $prefix ?: '';
    }
}
