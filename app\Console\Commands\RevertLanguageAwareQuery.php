<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\File;

class RevertLanguageAwareQuery extends Command
{
    protected $signature = 'revert:language-aware-query';
    protected $description = 'Revert the language-aware slug query to original state';

    public function handle()
    {
        $this->info('=== REVERTING LANGUAGE-AWARE SLUG QUERY ===');
        $this->newLine();

        $filePath = 'platform/plugins/language-advanced/src/Providers/HookServiceProvider.php';
        
        if (!File::exists($filePath)) {
            $this->error("File not found: {$filePath}");
            return 1;
        }

        $content = File::get($filePath);
        
        // Find the current modified method
        $modifiedMethod = '    public function getSlugQuery(EloquentBuilder $query, array $condition = []): EloquentBuilder
    {
        try {
            return $query
                ->orWhereHas(\'translations\', function (EloquentBuilder $query) use ($condition) {
                    $query->where($condition);
                    
                    // Make slug resolution language-aware
                    $currentLocale = Language::getCurrentLocale();
                    if ($currentLocale && $currentLocale !== Language::getDefaultLocale()) {
                        // Convert short locale to full locale format (e.g., ru -> ru_RU)
                        $supportedLocales = Language::getSupportedLocales();
                        $fullLocale = null;
                        
                        foreach ($supportedLocales as $localeCode => $localeData) {
                            if (substr($localeCode, 0, 2) === $currentLocale) {
                                $fullLocale = $localeCode;
                                break;
                            }
                        }
                        
                        if ($fullLocale) {
                            $query->where(\'lang_code\', $fullLocale);
                        }
                    }
                    
                    return $query;
                });
        } catch (Throwable) {
            return $query;
        }
    }';

        // Original method
        $originalMethod = '    public function getSlugQuery(EloquentBuilder $query, array $condition = []): EloquentBuilder
    {
        try {
            return $query
                ->orWhereHas(\'translations\', function (EloquentBuilder $query) use ($condition) {
                    return $query->where($condition);
                });
        } catch (Throwable) {
            return $query;
        }
    }';

        if (strpos($content, $modifiedMethod) === false) {
            $this->error('Could not find the modified method to revert.');
            return 1;
        }

        $newContent = str_replace($modifiedMethod, $originalMethod, $content);
        
        File::put($filePath, $newContent);
        $this->info('✅ Successfully reverted the getSlugQuery method to original state!');
        $this->newLine();
        $this->info('Both URLs should work again now.');
        $this->warn('⚠️  Clear cache after this change:');
        $this->line('php artisan cache:clear && php artisan config:clear && php artisan route:clear');

        return 0;
    }
}
