<?php

namespace Xmetr\RealEstate\Http\Controllers\Fronts;

use Xmetr\Base\Facades\EmailHandler;
use Xmetr\Base\Http\Controllers\BaseController;
use Xmetr\Base\Supports\RepositoryHelper;
use Xmetr\Location\Models\City;
use Xmetr\Location\Models\State;
use Xmetr\RealEstate\Enums\ConsultCustomFieldTypeEnum;
use Xmetr\RealEstate\Facades\RealEstateHelper;
use Xmetr\RealEstate\Forms\Fronts\ConsultForm;
use Xmetr\RealEstate\Http\Requests\SendConsultRequest;
use Xmetr\RealEstate\Models\Account;
use Xmetr\RealEstate\Models\Consult;
use Xmetr\RealEstate\Models\ConsultCustomField;
use Xmetr\RealEstate\Models\Currency;
use Xmetr\RealEstate\Repositories\Interfaces\ProjectInterface;
use Xmetr\RealEstate\Repositories\Interfaces\PropertyInterface;
use Xmetr\SeoHelper\Facades\SeoHelper;
use Xmetr\Theme\Facades\Theme;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;
use Xmetr\Location\Models\Country;
use Xmetr\Media\Facades\RvMedia;
use Xmetr\Page\Models\Page;
use Xmetr\SeoHelper\SeoOpenGraph;
use Xmetr\Slug\Facades\SlugHelper;
use Illuminate\Support\Str;

class PublicController extends BaseController
{
    public function postSendConsult(
        SendConsultRequest $request,
        PropertyInterface $propertyRepository,
        ProjectInterface $projectRepository
    ) {
        do_action('form_extra_fields_validate', $request, ConsultForm::class);

        try {
            $sendTo = null;
            $link = null;
            $subject = null;

            if ($request->input('type') == 'project') {
                $request->merge(['project_id' => $request->input('data_id')]);
                $project = $projectRepository->findById($request->input('data_id'));
                if ($project) {
                    $link = $project->url;
                    $subject = $project->name;
                }
            } else {
                $request->merge(['property_id' => $request->input('data_id')]);
                $property = $propertyRepository->findById($request->input('data_id'), ['author']);
                if ($property) {
                    $link = $property->url;
                    $subject = $property->name;

                    if ($property->author->email) {
                        $sendTo = $property->author->email;
                    }
                }
            }

            $data = [
                ...$request->input(),
                'ip_address' => $request->ip(),
            ];

            if (Arr::has($data, 'consult_custom_fields')) {
                $customFields = ConsultCustomField::query()
                    ->wherePublished()
                    ->with('options')
                    ->get();

                $data['custom_fields'] = collect($data['consult_custom_fields'])
                    ->mapWithKeys(function ($item, $id) use ($customFields) {
                        $field = $customFields->firstWhere('id', $id);
                        $options = $field->options->firstWhere('value', $item);

                        if (! $field) {
                            return [];
                        }

                        $value = match ($field->type->getValue()) {
                            ConsultCustomFieldTypeEnum::CHECKBOX => $item ? __('Yes') : __('No'),
                            ConsultCustomFieldTypeEnum::RADIO, ConsultCustomFieldTypeEnum::DROPDOWN => $options?->label,
                            default => $item,
                        };

                        return [$field->name => $value];
                    })->all();
            }

            $consult = Consult::query()->create($data);

            EmailHandler::setModule(REAL_ESTATE_MODULE_SCREEN_NAME)
                ->setVariableValues([
                    'consult_name' => $consult->name,
                    'consult_email' => $consult->email,
                    'consult_phone' => $consult->phone,
                    'consult_content' => $consult->content,
                    'consult_link' => $link,
                    'consult_subject' => $subject,
                    'consult_ip_address' => $consult->ip_address,
                    'consult_custom_fields' => $data['custom_fields'] ?? [],
                ])
                ->sendUsingTemplate('notice', $sendTo);

            return $this
                ->httpResponse()
                ->setMessage(trans('plugins/real-estate::consult.email.success'));
        } catch (Exception $exception) {
            info($exception->getMessage());

            return $this
                ->httpResponse()
                ->setError()
                ->setMessage(trans('plugins/real-estate::consult.email.failed'));
        }
    }

    public function getProjects(Request $request)
    {
        SeoHelper::setTitle(__('Projects'));

        $projects = RealEstateHelper::getProjectsFilter((int) theme_option('number_of_projects_per_page') ?: 12, RealEstateHelper::getReviewExtraData());

        if ($request->ajax()) {
            if ($request->input('minimal')) {
                return $this
                    ->httpResponse()
                    ->setData(Theme::partial('search-suggestion', ['items' => $projects]));
            }

            $view = Theme::getThemeNamespace('partials.real-estate.projects.items');

            if (! view()->exists($view)) {
                $view = Theme::getThemeNamespace('views.real-estate.projects.index');
            }

            return $this
                ->httpResponse()
                ->setData(view($view, compact('projects'))->render());
        }

        return Theme::scope('real-estate.projects', compact('projects'), 'plugins/real-estate::themes.projects')->render();
    }

    public function getProperties(Request $request)
    {
        $categoryName = null;
        $countryName = null;
        $cityName = null;
        $bedrooms = $request->input('bedroom');
        $category_seo_title = null;

        // Check for excluded filters
        $hasExtraFilters = $request->hasAny([
            'suitable',
            'account_type',
            'floor',
            'min_price',
            'max_price',
            'furnished',
            'smoking_allowed',
            'features',
            'spoken_languages',
        ]);

        // Get category name
        if ($request->input('category')) {
            $category = get_property_category_by_slug($request->input('category'));
            if ($category) {
                $categoryName = $category->name;
                $category_seo = $category->getMetadata('seo_meta', true);
                $category_seo_title = $category_seo['seo_title'] ?? $category->name;
            }
        }

        // Get country name
        if ($request->input('country_id') || $request->input('city_id')) {
            if ($request->input('country_id')) {
                $countryName = get_country_name_by_id($request->input('country_id'));
            } elseif ($request->input('city_id')) {
                $countryName = get_country_name_by_id_of_city($request->input('city_id'));
            }
        }

        // Get city name
        if ($request->input('city_id')) {
            $cityName = get_city_name_by_id($request->input('city_id'));
        }

        if (!$hasExtraFilters) {
             // ✅ If only category is present → use its SEO title directly
                if ($categoryName && !$countryName && !$cityName && !$bedrooms) {
                    $page_title = $category_seo_title;
                    SeoHelper::setTitle($page_title);

                } elseif ($countryName || $cityName || $categoryName || $bedrooms) {
                $parts = [];
                $parts[] = 'Long-Term';

                if ($bedrooms) {
                    $parts[] = $bedrooms . ' Bedrooms';
                }

                if ($categoryName) {
                    $parts[] = $categoryName;
                }

                $parts[] = 'Rentals in';

                $locationParts = [];
                if ($countryName) {
                    $locationParts[] = $countryName;
                }
                if ($cityName) {
                    $locationParts[] = $cityName;
                }

                $parts[] = implode(' ', $locationParts);

                $page_title = implode(' ', $parts);

                SeoHelper::setTitle($page_title);
            } else {

                $propertiesListPage =  Page::query()
                    ->wherePublished()
                    ->where('id', theme_option('properties_list_page_id'))
                    ->select(['id', 'name'])
                    ->with(['slugable'])
                    ->first();
                // Theme::breadcrumb()->add($propertiesListPage->name, $propertiesListPage->url);
                $properties_seo = $propertiesListPage->getMetadata('seo_meta', true);
                $page_title = $properties_seo['seo_title'] ?? $propertiesListPage->name;
                SeoHelper::setTitle($page_title);
            }
        } else {
            // If extra filters → use fallback
            $propertiesListPage =  Page::query()
                ->wherePublished()
                ->where('id', theme_option('properties_list_page_id'))
                ->select(['id', 'name'])
                ->with(['slugable'])
                ->first();
            // Theme::breadcrumb()->add($propertiesListPage->name, $propertiesListPage->url);
            $properties_seo = $propertiesListPage->getMetadata('seo_meta', true);
            $page_title = $properties_seo['seo_title'] ?? $propertiesListPage->name;
            SeoHelper::setTitle($page_title);
        }

        $properties = RealEstateHelper::getPropertiesFilter((int) theme_option('number_of_properties_per_page') ?: 12, RealEstateHelper::getReviewExtraData());

        if ($request->ajax()) {
            if ($request->query('minimal')) {
                return $this
                    ->httpResponse()
                    ->setData(Theme::partial('search-suggestion', ['items' => $properties]));
            }

            $view = Theme::getThemeNamespace('partials.real-estate.properties.items');

            if (! view()->exists($view)) {
                $view = Theme::getThemeNamespace('views.real-estate.properties.index');
            }

            return $this
                ->httpResponse()
                ->setData(view($view, compact('properties', 'page_title'))->render());
        }

        return Theme::scope('real-estate.properties', compact('properties', 'page_title'), 'plugins/real-estate::themes.properties')->render();
    }

    public function changeCurrency(Request $request, $title = null)
    {
        if (empty($title)) {
            $title = $request->input('currency');
        }

        if (! $title) {
            return $this->httpResponse();
        }

        /**
         * @var Currency $currency
         */
        $currency = Currency::query()
            ->where('title', $title)
            ->first();

        if ($currency) {
            cms_currency()->setApplicationCurrency($currency);
        }

        return $this->httpResponse();
    }

    public function getProjectsByCity(string $slug, Request $request)
    {
        $city = City::query()->wherePublished()->where('slug', $slug)->firstOrFail();

        SeoHelper::setTitle(__('Projects in :city', ['city' => $city->name]));

        Theme::breadcrumb()
            ->add(SeoHelper::getTitle(), route('public.projects-by-city', $city->slug));

        do_action(BASE_ACTION_PUBLIC_RENDER_SINGLE, CITY_MODULE_SCREEN_NAME, $city);

        $perPage = $request->integer('per_page') ?: (int) theme_option('number_of_projects_per_page', 12);

        $request->merge(['city' => $slug, 'city_id' => $city->id]);

        $projects = RealEstateHelper::getProjectsFilter($perPage, RealEstateHelper::getReviewExtraData());

        if ($request->ajax()) {
            if ($request->input('minimal')) {
                return $this
                    ->httpResponse()
                    ->setData(Theme::partial('search-suggestion', ['items' => $projects]));
            }

            return $this
                ->httpResponse()
                ->setData(Theme::partial('real-estate.projects.items', ['projects' => $projects]));
        }

        return Theme::scope('real-estate.projects', [
            'projects' => $projects,
            'ajaxUrl' => route('public.projects-by-city', $city->slug),
            'actionUrl' => route('public.projects-by-city', $city->slug),
        ], 'plugins/real-estate::themes.projects')
            ->render();
    }

    public function getPropertiesByCity(string $slug, Request $request)
    {
        $city = City::query()->wherePublished()->where('slug', $slug)->firstOrFail();

        SeoHelper::setTitle(__('Properties in :city', ['city' => $city->name]));

        do_action(BASE_ACTION_PUBLIC_RENDER_SINGLE, CITY_MODULE_SCREEN_NAME, $city);

        Theme::breadcrumb()
            ->add(SeoHelper::getTitle(), route('public.properties-by-city', $city->slug));

        $perPage = $request->integer('per_page') ?: (int) theme_option('number_of_properties_per_page', 12);

        $request->merge(['city' => $slug, 'city_id' => $city->id, 'country_id' => $city->country->id]);

        $properties = RealEstateHelper::getPropertiesFilter($perPage, RealEstateHelper::getReviewExtraData());

        if ($request->ajax()) {
            if ($request->input('minimal')) {
                return $this
                    ->httpResponse()
                    ->setData(Theme::partial('search-suggestion', ['items' => $properties]));
            }

            return $this
                ->httpResponse()
                ->setData(Theme::partial('real-estate.properties.items', ['properties' => $properties]));
        }

        return Theme::scope('real-estate.properties', [
            'properties' => $properties,
            'ajaxUrl' => route('public.properties-by-city', $city->slug),
            'actionUrl' => route('public.properties-by-city', $city->slug),
        ], 'plugins/real-estate::themes.properties')
            ->render();
    }

    public function getPropertiesByCountryCity(string $country_slug, string $city_slug, Request $request)
    {
        $prefixOfCountryCityPage = RealEstateHelper::getPageSlug('properties_country_city');

        $countrySlug = SlugHelper::getSlug($country_slug, $prefixOfCountryCityPage, Country::class);
        $citySlug = SlugHelper::getSlug($city_slug, $prefixOfCountryCityPage, City::class);

        // If still not found, return 404
        abort_unless($countrySlug && $citySlug, 404);

        $city = City::query()
            ->wherePublished()
            ->where('id', $citySlug->reference_id)
            ->firstOrFail();

        $country = Country::query()
            ->wherePublished()
            ->where('id', $countrySlug->reference_id)
            ->firstOrFail();

        $city->loadMissing('metadata');

        $seo_meta = $city->getMetaData('seo_meta', true);

        SeoHelper::setTitle($seo_meta['seo_title'] ?? $city->name)
            ->setDescription($seo_meta['seo_description'] ?? Str::words($city->description, 120));

        $meta = new SeoOpenGraph();
        if ($city->image) {
            $meta->setImage(RvMedia::getImageUrl($city->image));
        }
        $meta->setDescription($city->description);
        $meta->setUrl($city->url);
        $meta->setTitle($city->name);
        $meta->setType('article');

        SeoHelper::setSeoOpenGraph($meta);

        SeoHelper::meta()->setUrl($city->url);

        do_action(BASE_ACTION_PUBLIC_RENDER_SINGLE, CITY_MODULE_SCREEN_NAME, $city);

        if (theme_option('properties_list_page_id')) {
                    $propertiesListPage =  Page::query()
                                ->wherePublished()
                                ->where('id', theme_option('properties_list_page_id'))
                                ->select(['id', 'name'])
                                ->with(['slugable'])
                                ->first();
                    Theme::breadcrumb()->add($propertiesListPage->name, $propertiesListPage->url);
                }

        Theme::breadcrumb()->add($country->name, $country->url);
        Theme::breadcrumb()->add($city->name, $city->url);

        $perPage = $request->integer('per_page') ?: (int) theme_option('number_of_properties_per_page', 12);

        $request->merge(['country_id' => $country->id, 'city_id' => $city->id]);

        $properties = RealEstateHelper::getPropertiesFilter($perPage, RealEstateHelper::getReviewExtraData());

        if ($request->ajax()) {
            if ($request->input('minimal')) {
                return $this
                    ->httpResponse()
                    ->setData(Theme::partial('search-suggestion', ['items' => $properties]));
            }

            return $this
                ->httpResponse()
                ->setData(Theme::partial('real-estate.properties.items', ['properties' => $properties]));
        }

        return Theme::scope('real-estate.properties', [
            'properties' => $properties,
            'city' => $city,
            'country' => $country,
        ], 'plugins/real-estate::themes.properties')
            ->render();
    }

    public function getProjectsByState(string $slug, Request $request)
    {
        $state = State::query()
            ->wherePublished()
            ->where('slug', $slug)
            ->firstOrFail();

        SeoHelper::setTitle(__('Projects in :state', ['state' => $state->name]));

        Theme::breadcrumb()
            ->add(SeoHelper::getTitle(), route('public.projects-by-city', $state->slug));

        do_action(BASE_ACTION_PUBLIC_RENDER_SINGLE, STATE_MODULE_SCREEN_NAME, $state);

        $perPage = $request->integer('per_page') ?: (int) theme_option('number_of_projects_per_page', 12);

        $request->merge(['state' => $slug, 'state_id' => $state->id]);

        $projects = RealEstateHelper::getProjectsFilter($perPage, RealEstateHelper::getReviewExtraData());

        if ($request->ajax()) {
            if ($request->input('minimal')) {
                return $this
                    ->httpResponse()
                    ->setData(Theme::partial('search-suggestion', ['items' => $projects]));
            }

            return $this
                ->httpResponse()
                ->setData(Theme::partial('real-estate.projects.items', ['projects' => $projects]));
        }

        return Theme::scope('real-estate.projects', [
            'projects' => $projects,
            'ajaxUrl' => route('public.projects-by-state', $state->slug),
            'actionUrl' => route('public.projects-by-state', $state->slug),
        ], 'plugins/real-estate::themes.projects')
            ->render();
    }

    public function getPropertiesByState(
        string $slug,
        Request $request
    ) {
        $state = State::query()
            ->wherePublished()
            ->where('slug', $slug)
            ->firstOrFail();

        SeoHelper::setTitle(__('Properties in :state', ['state' => $state->name]));

        do_action(BASE_ACTION_PUBLIC_RENDER_SINGLE, STATE_MODULE_SCREEN_NAME, $state);

        Theme::breadcrumb()
            ->add(SeoHelper::getTitle(), route('public.properties-by-state', $state->slug));

        $perPage = $request->integer('per_page') ?: (int) theme_option('number_of_properties_per_page', 12);

        $request->merge(['state' => $slug, 'state_id' => $state->id]);

        $properties = RealEstateHelper::getPropertiesFilter($perPage, RealEstateHelper::getReviewExtraData());

        if ($request->ajax()) {
            if ($request->input('minimal')) {
                return $this
                    ->httpResponse()
                    ->setData(Theme::partial('search-suggestion', ['items' => $properties]));
            }

            return $this
                ->httpResponse()
                ->setData(Theme::partial('real-estate.properties.items', ['properties' => $properties]));
        }

        return Theme::scope('real-estate.properties', [
            'properties' => $properties,
            'ajaxUrl' => route('public.properties-by-state', $state->slug),
            'actionUrl' => route('public.properties-by-state', $state->slug),
        ], 'plugins/real-estate::themes.properties')
            ->render();
    }

    public function getAgents()
    {
        abort_if(RealEstateHelper::isDisabledPublicProfile(), 404);

        $accounts = Account::query()
            ->where('is_public_profile', true)
            ->orderByDesc('is_featured')
            ->orderBy('first_name')
            ->withCount([
                'properties' => function ($query) {
                    return RepositoryHelper::applyBeforeExecuteQuery($query, $query->getModel());
                },
            ])
            ->with(['avatar'])
            ->paginate(12);

        SeoHelper::setTitle(__('Agents'));

        Theme::breadcrumb()->add(__('Agents'), route('public.agents'));

        return Theme::scope('real-estate.agents', compact('accounts'), 'plugins/real-estate::themes.agents')->render();
    }
}
