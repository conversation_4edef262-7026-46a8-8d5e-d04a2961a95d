<?php return array (
  'barryvdh/laravel-debugbar' => 
  array (
    'aliases' => 
    array (
      'Debugbar' => 'Barryvdh\\Debugbar\\Facades\\Debugbar',
    ),
    'providers' => 
    array (
      0 => 'Barryvdh\\Debugbar\\ServiceProvider',
    ),
  ),
  'barryvdh/laravel-dompdf' => 
  array (
    'aliases' => 
    array (
      'PDF' => 'Barryvdh\\DomPDF\\Facade\\Pdf',
      'Pdf' => 'Barryvdh\\DomPDF\\Facade\\Pdf',
    ),
    'providers' => 
    array (
      0 => 'Barryvdh\\DomPDF\\ServiceProvider',
    ),
  ),
  'laravel/sail' => 
  array (
    'providers' => 
    array (
      0 => 'Laravel\\Sail\\SailServiceProvider',
    ),
  ),
  'laravel/sanctum' => 
  array (
    'providers' => 
    array (
      0 => 'Laravel\\Sanctum\\SanctumServiceProvider',
    ),
  ),
  'laravel/socialite' => 
  array (
    'aliases' => 
    array (
      'Socialite' => 'Laravel\\Socialite\\Facades\\Socialite',
    ),
    'providers' => 
    array (
      0 => 'Laravel\\Socialite\\SocialiteServiceProvider',
    ),
  ),
  'laravel/tinker' => 
  array (
    'providers' => 
    array (
      0 => 'Laravel\\Tinker\\TinkerServiceProvider',
    ),
  ),
  'maatwebsite/excel' => 
  array (
    'aliases' => 
    array (
      'Excel' => 'Maatwebsite\\Excel\\Facades\\Excel',
    ),
    'providers' => 
    array (
      0 => 'Maatwebsite\\Excel\\ExcelServiceProvider',
    ),
  ),
  'mews/purifier' => 
  array (
    'aliases' => 
    array (
      'Purifier' => 'Mews\\Purifier\\Facades\\Purifier',
    ),
    'providers' => 
    array (
      0 => 'Mews\\Purifier\\PurifierServiceProvider',
    ),
  ),
  'mollie/laravel-mollie' => 
  array (
    'aliases' => 
    array (
      'Mollie' => 'Mollie\\Laravel\\Facades\\Mollie',
    ),
    'providers' => 
    array (
      0 => 'Mollie\\Laravel\\MollieServiceProvider',
    ),
  ),
  'nesbot/carbon' => 
  array (
    'providers' => 
    array (
      0 => 'Carbon\\Laravel\\ServiceProvider',
    ),
  ),
  'nunomaduro/collision' => 
  array (
    'providers' => 
    array (
      0 => 'NunoMaduro\\Collision\\Adapters\\Laravel\\CollisionServiceProvider',
    ),
  ),
  'nunomaduro/termwind' => 
  array (
    'providers' => 
    array (
      0 => 'Termwind\\Laravel\\TermwindServiceProvider',
    ),
  ),
  'spatie/laravel-ignition' => 
  array (
    'aliases' => 
    array (
      'Flare' => 'Spatie\\LaravelIgnition\\Facades\\Flare',
    ),
    'providers' => 
    array (
      0 => 'Spatie\\LaravelIgnition\\IgnitionServiceProvider',
    ),
  ),
  'tightenco/ziggy' => 
  array (
    'providers' => 
    array (
      0 => 'Tighten\\Ziggy\\ZiggyServiceProvider',
    ),
  ),
  'unicodeveloper/laravel-paystack' => 
  array (
    'aliases' => 
    array (
      'Paystack' => 'Unicodeveloper\\Paystack\\Facades\\Paystack',
    ),
    'providers' => 
    array (
      0 => 'Unicodeveloper\\Paystack\\PaystackServiceProvider',
    ),
  ),
  'xmetr/api' => 
  array (
    'providers' => 
    array (
      0 => 'Xmetr\\Api\\Providers\\ApiServiceProvider',
    ),
    'aliases' => 
    array (
      'ApiHelper' => 'Xmetr\\Api\\Facades\\ApiHelper',
    ),
  ),
  'xmetr/assets' => 
  array (
    'providers' => 
    array (
      0 => 'Xmetr\\Assets\\Providers\\AssetsServiceProvider',
    ),
    'aliases' => 
    array (
      'Assets' => 'Xmetr\\Assets\\Facades\\AssetsFacade',
    ),
  ),
  'xmetr/data-synchronize' => 
  array (
    'providers' => 
    array (
      0 => 'Xmetr\\DataSynchronize\\Providers\\DataSynchronizeServiceProvider',
    ),
  ),
  'xmetr/dev-tool' => 
  array (
    'providers' => 
    array (
      0 => 'Xmetr\\DevTool\\Providers\\DevToolServiceProvider',
    ),
  ),
  'xmetr/form-builder' => 
  array (
    'providers' => 
    array (
      0 => 'Kris\\LaravelFormBuilder\\FormBuilderServiceProvider',
    ),
    'aliases' => 
    array (
      'FormBuilder' => 'Kris\\LaravelFormBuilder\\Facades\\FormBuilder',
    ),
  ),
  'xmetr/get-started' => 
  array (
    'providers' => 
    array (
      0 => 'Xmetr\\GetStarted\\Providers\\GetStartedServiceProvider',
    ),
  ),
  'xmetr/git-commit-checker' => 
  array (
    'providers' => 
    array (
      0 => 'Xmetr\\GitCommitChecker\\Providers\\GitCommitCheckerServiceProvider',
    ),
  ),
  'xmetr/installer' => 
  array (
    'providers' => 
    array (
      0 => 'Xmetr\\Installer\\Providers\\InstallerServiceProvider',
    ),
  ),
  'xmetr/menu' => 
  array (
    'providers' => 
    array (
      0 => 'Xmetr\\Menu\\Providers\\MenuServiceProvider',
    ),
    'aliases' => 
    array (
      'Menu' => 'Xmetr\\Menu\\Facades\\Menu',
    ),
  ),
  'xmetr/optimize' => 
  array (
    'providers' => 
    array (
      0 => 'Xmetr\\Optimize\\Providers\\OptimizeServiceProvider',
    ),
    'aliases' => 
    array (
      'OptimizerHelper' => 'Xmetr\\Optimize\\Facades\\OptimizerHelper',
    ),
  ),
  'xmetr/page' => 
  array (
    'providers' => 
    array (
      0 => 'Xmetr\\Page\\Providers\\PageServiceProvider',
    ),
  ),
  'xmetr/platform' => 
  array (
    'providers' => 
    array (
      0 => 'Xmetr\\Base\\Providers\\BaseServiceProvider',
      1 => 'Xmetr\\Base\\Providers\\CommandServiceProvider',
      2 => 'Xmetr\\Base\\Providers\\EventServiceProvider',
      3 => 'Xmetr\\Base\\Providers\\ComposerServiceProvider',
      4 => 'Xmetr\\Base\\Providers\\MailConfigServiceProvider',
      5 => 'Xmetr\\Base\\Providers\\FormServiceProvider',
      6 => 'Xmetr\\Support\\Providers\\SupportServiceProvider',
      7 => 'Xmetr\\Table\\Providers\\TableServiceProvider',
      8 => 'Xmetr\\ACL\\Providers\\AclServiceProvider',
      9 => 'Xmetr\\Dashboard\\Providers\\DashboardServiceProvider',
      10 => 'Xmetr\\Media\\Providers\\MediaServiceProvider',
      11 => 'Xmetr\\JsValidation\\Providers\\JsValidationServiceProvider',
      12 => 'Xmetr\\Chart\\Providers\\ChartServiceProvider',
      13 => 'Xmetr\\Icon\\Providers\\IconServiceProvider',
    ),
    'aliases' => 
    array (
      'Action' => 'Xmetr\\Base\\Facades\\Action',
      'AdminAppearance' => 'Xmetr\\Base\\Facades\\AdminAppearance',
      'AdminHelper' => 'Xmetr\\Base\\Facades\\AdminHelper',
      'Assets' => 'Xmetr\\Base\\Facades\\Assets',
      'BaseHelper' => 'Xmetr\\Base\\Facades\\BaseHelper',
      'Breadcrumbs' => 'Xmetr\\Base\\Facades\\Breadcrumbs',
      'DashboardMenu' => 'Xmetr\\Base\\Facades\\DashboardMenu',
      'CoreIcon' => 'Xmetr\\Icon\\Facades\\Icon',
      'EmailHandler' => 'Xmetr\\Base\\Facades\\EmailHandler',
      'Filter' => 'Xmetr\\Base\\Facades\\Filter',
      'Form' => 'Xmetr\\Base\\Facades\\Form',
      'Html' => 'Xmetr\\Base\\Facades\\Html',
      'JsValidator' => 'Xmetr\\JsValidation\\Facades\\JsValidator',
      'MacroableModels' => 'Xmetr\\Base\\Facades\\MacroableModels',
      'MetaBox' => 'Xmetr\\Base\\Facades\\MetaBox',
      'PageTitle' => 'Xmetr\\Base\\Facades\\PageTitle',
      'PanelSectionManager' => 'Xmetr\\Base\\Facades\\PanelSectionManager',
      'RvMedia' => 'Xmetr\\Media\\Facades\\RvMedia',
      'Setting' => 'Xmetr\\Setting\\Facades\\Setting',
    ),
  ),
  'xmetr/plugin-management' => 
  array (
    'providers' => 
    array (
      0 => 'Xmetr\\PluginManagement\\Providers\\PluginManagementServiceProvider',
    ),
  ),
  'xmetr/revision' => 
  array (
    'providers' => 
    array (
      0 => 'Xmetr\\Revision\\Providers\\RevisionServiceProvider',
    ),
  ),
  'xmetr/seo-helper' => 
  array (
    'providers' => 
    array (
      0 => 'Xmetr\\SeoHelper\\Providers\\SeoHelperServiceProvider',
    ),
    'aliases' => 
    array (
      'SeoHelper' => 'Xmetr\\SeoHelper\\Facades\\SeoHelper',
    ),
  ),
  'xmetr/shortcode' => 
  array (
    'providers' => 
    array (
      0 => 'Xmetr\\Shortcode\\Providers\\ShortcodeServiceProvider',
    ),
    'aliases' => 
    array (
      'Shortcode' => 'Xmetr\\Shortcode\\Facades\\Shortcode',
    ),
  ),
  'xmetr/sitemap' => 
  array (
    'providers' => 
    array (
      0 => 'Xmetr\\Sitemap\\Providers\\SitemapServiceProvider',
    ),
  ),
  'xmetr/slug' => 
  array (
    'providers' => 
    array (
      0 => 'Xmetr\\Slug\\Providers\\SlugServiceProvider',
    ),
    'aliases' => 
    array (
      'SlugHelper' => 'Xmetr\\Slug\\Facades\\SlugHelper',
    ),
  ),
  'xmetr/theme' => 
  array (
    'providers' => 
    array (
      0 => 'Xmetr\\Theme\\Providers\\ThemeServiceProvider',
      1 => 'Xmetr\\Theme\\Providers\\RouteServiceProvider',
    ),
    'aliases' => 
    array (
      'Theme' => 'Xmetr\\Theme\\Facades\\Theme',
      'ThemeOption' => 'Xmetr\\Theme\\Facades\\ThemeOption',
      'ThemeManager' => 'Xmetr\\Theme\\Facades\\Manager',
      'AdminBar' => 'Xmetr\\Theme\\Facades\\AdminBar',
      'SiteMapManager' => 'Xmetr\\Theme\\Facades\\SiteMapManager',
    ),
  ),
  'xmetr/widget' => 
  array (
    'providers' => 
    array (
      0 => 'Xmetr\\Widget\\Providers\\WidgetServiceProvider',
    ),
    'aliases' => 
    array (
      'Widget' => 'Xmetr\\Widget\\Facades\\Widget',
      'WidgetGroup' => 'Xmetr\\Widget\\Facades\\WidgetGroup',
    ),
  ),
  'yajra/laravel-datatables-buttons' => 
  array (
    'providers' => 
    array (
      0 => 'Yajra\\DataTables\\ButtonsServiceProvider',
    ),
  ),
  'yajra/laravel-datatables-html' => 
  array (
    'providers' => 
    array (
      0 => 'Yajra\\DataTables\\HtmlServiceProvider',
    ),
  ),
  'yajra/laravel-datatables-oracle' => 
  array (
    'aliases' => 
    array (
      'DataTables' => 'Yajra\\DataTables\\Facades\\DataTables',
    ),
    'providers' => 
    array (
      0 => 'Yajra\\DataTables\\DataTablesServiceProvider',
    ),
  ),
);