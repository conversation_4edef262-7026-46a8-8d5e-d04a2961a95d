<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Xmetr\Language\Facades\Language;
use Xmetr\Slug\Facades\SlugHelper;

class RedirectIncorrectLanguagePrefix
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        // Only process GET requests for public routes
        if (!$request->isMethod('GET') || $request->is('admin/*') || $request->is('api/*')) {
            return $next($request);
        }

        // Get the current path segments
        $segments = array_filter(explode('/', $request->path()));
        
        // Check if this is a language-prefixed URL with at least 3 segments: [lang]/[prefix]/[slug]
        if (count($segments) < 3) {
            return $next($request);
        }

        $langCode = $segments[0];
        $prefix = $segments[1];
        $slug = $segments[2];

        // Check if the first segment is a valid language code
        $supportedLocales = Language::getSupportedLocales();
        if (!isset($supportedLocales[$langCode]) && !$this->isShortLanguageCode($langCode, $supportedLocales)) {
            return $next($request);
        }

        // Get the correct prefix for this language and slug
        $correctPrefix = $this->getCorrectPrefixForLanguageAndSlug($langCode, $slug);
        
        // If we found a correct prefix and it's different from the current one, redirect
        if ($correctPrefix && $correctPrefix !== $prefix) {
            $newPath = $langCode . '/' . $correctPrefix . '/' . $slug;
            
            // Preserve any additional path segments
            if (count($segments) > 3) {
                $additionalSegments = array_slice($segments, 3);
                $newPath .= '/' . implode('/', $additionalSegments);
            }
            
            // Preserve query parameters
            $queryString = $request->getQueryString();
            if ($queryString) {
                $newPath .= '?' . $queryString;
            }
            
            return redirect($newPath, 301); // Permanent redirect
        }

        return $next($request);
    }

    /**
     * Check if a language code is a short version of a supported locale
     */
    private function isShortLanguageCode(string $langCode, array $supportedLocales): bool
    {
        foreach (array_keys($supportedLocales) as $locale) {
            if (substr($locale, 0, 2) === $langCode) {
                return true;
            }
        }
        return false;
    }

    /**
     * Get the correct prefix for a given language and slug
     */
    private function getCorrectPrefixForLanguageAndSlug(string $langCode, string $slug): ?string
    {
        // Convert short language code to full locale if needed
        $fullLocale = $this->getFullLocale($langCode);
        if (!$fullLocale) {
            return null;
        }

        // First, try to find the slug in translations for this language
        $translation = DB::table('slugs_translations')
            ->where('lang_code', $fullLocale)
            ->where('key', $slug)
            ->first();

        if ($translation) {
            return $translation->prefix;
        }

        // If not found in translations, check if it exists in main slugs table
        $mainSlug = DB::table('slugs')
            ->where('key', $slug)
            ->first();

        if ($mainSlug) {
            // Get the correct prefix for this model type and language
            return $this->getCorrectPrefixForModelAndLanguage($mainSlug->reference_type, $langCode);
        }

        return null;
    }

    /**
     * Convert short language code to full locale
     */
    private function getFullLocale(string $langCode): ?string
    {
        $supportedLocales = Language::getSupportedLocales();
        
        // If it's already a full locale, return it
        if (isset($supportedLocales[$langCode])) {
            return $langCode;
        }

        // Find the full locale for this short code
        foreach (array_keys($supportedLocales) as $locale) {
            if (substr($locale, 0, 2) === $langCode) {
                return $locale;
            }
        }

        return null;
    }

    /**
     * Get the correct prefix for a model type and language
     */
    private function getCorrectPrefixForModelAndLanguage(string $modelClass, string $langCode): ?string
    {
        // Get the permalink setting key for this model
        $settingKey = SlugHelper::getPermalinkSettingKey($modelClass);
        
        // Get the language-specific setting key using short code
        $langSettingKey = $settingKey . '-' . $langCode;
        
        // Get the setting value for this specific language
        $prefix = setting($langSettingKey);
        
        // If no language-specific setting exists, fall back to default
        if (!$prefix) {
            $prefix = setting($settingKey);
        }
        
        // If still no prefix, get from SlugHelper
        if (!$prefix) {
            $prefix = SlugHelper::getPrefix($modelClass, '', false);
        }
        
        return $prefix ?: null;
    }
}
